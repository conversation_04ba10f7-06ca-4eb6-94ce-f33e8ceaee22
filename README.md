## 常见具体函数定义域
   1）y=1/x     x≠0
   2）y=2n√x    x≥0
   3）y=2n+1√x  x∈(-∞，+∞)     3倍√1=1   3倍√-1=-1  (-1)³=-1
   4）y=loga^x  x>0            lnx    x>0
   5）y=tanx    x≠kπ+π/2
   6）y=cotx    x≠kπ
   7）y=arctanx y=arccotx   x∈R，x∈(-∞，+∞)
   8）y=arcsinx y=arccosx   x∈[-1,1]


##  和角与差角公式
      和角公式：sin(α±β)= sinαcosβ ± cosαsinβ
      差角公式：cos(α±β)= cosαcosβ ∓ sinαsinβ

      查漏补缺之自然对数

      ln的运算法则    M>0, N>0
      ln(M*N)=lnM+lnN  
      ln(M/N)=lnM-lnN
      ln(M^n)=n*lnM
      ln1=0
      lne=1
      
      ln是自然对数，是以e为底的对数。
      log是常用并且以10为底的对数，也是一般的对数，能以任何大于0且不等于1的数为底。
      自然对数间的转换  logN=lnN/ln10、lnN=logN/loge


## 函数四种基本特性：
(https://www.bilibili.com/video/BV1HP411t7cS?p=4&vd_source=ff9fbf8f8f6ad634b004aac7b49b56b7)

### 1. 有界性
   定义域是指输入变量的取值范围，而值域是指输出变量的取值范围。函数的定义要求定义域和值域存在一种对应关系，这种关系可以通过函数的图像来显示。函数的图像是一条连续的曲线，横坐标表示输入变量，纵坐标表示输出变量。

   函数的定义域和值域通常都是实数集，可以是有限集合或无限集合。如果一个函数的定义域和值域之间存在一一对应的关系，那么这个函数就被称为有界函数（如果函数的图像在水平方向上有上下界，那么这个函数就是有界函数）。


### 2. 单调性
   函数的单调性是指函数在定义域内的变化趋势，如果一个函数在定义域内的某个区间内，随着输入变量的增加，输出变量也相应地增加，那么这个函数就被称为单调递增函数；如果随着输入变量的减少，输出变量也相应地减少，那么这个函数就被称为单调递减函数。

### 3. 奇偶性
   函数的奇偶性是指是否具有对称性。
   如果函数满足f(-x)=f(x)，那么这个函数就被称为偶函数； y=x² 偶函数
   如果函数满足f(-x)=-f(x)，那么这个函数就被称为奇函数  y=x³ 奇函数

   奇函数+奇函数=奇函数    奇函数×奇函数=偶函数
   偶函数+偶函数=偶函数    偶函数×偶函数=偶函数
   奇函数+偶函数=非奇非偶  奇函数×偶函数=奇函数
   如果一个函数是奇函数或者偶函数，首先应满足的条件是其定义域关于原点对称。如果定义域关于原点不对称，那么该函数肯定既不是奇函数也不是偶函数
   y=sinx 周期为2kπ，k∈z
   y=cosx 周期为2π

### 4. 周期性
   函数的周期性是指函数是否具有周期变化的特点，如果一个函数满足f(x+t)=f(x)，那么这个函数就被称为周期函数，其中t为该函数的周期
   

### 幂函数定义： y=x^a(a为常数)的函数，即以底数为自变量，幂为因变量，指数为常量的函数称为幂函数
   当a为不同的数值时
   1）如果a为任意实数，则函数的定义域为大于0的所有实数；
   2）如果a为负数，则x肯定不能为0

### 指数函数定义：
（1）指数函数的定义域为所有实数的集合，这里的前提是a大于0，对于a不大于0的情况，则必然使得函数的定义域不存在连续的区间，因此不考虑
（2）指数函数的值域为大于0的实数集合
（3）y=ln^x函数图像是单调递增  y=-ln^x函数图像是单调递减（凹函数）
（4）a大于1，则指数函数单调递增；a小于1大于0，则为单调递减。
（5）

### 反函数基本性质：
（1）反函数和原函数的图像关于直线y=x对称
（2）反函数和原函数单调性相同
（3）反函数和原函数奇偶性相同【奇函数的反函数还是奇函数，非奇非偶函数的反函数还是非奇非偶函数，偶函数不存在反函数】
（4）只有在连续定义域内单调的函数才有反函数



### 复合函数：
   自变量x ———u=g(x)——— 中间变量u ———y=f(u)———因变量
     由函数y=f(u)及u=g(x)构成的函数y=f[g(x)]称为复合函数
     1）注意复合函数的先后次序，y=f[g(x)]和g[f(x)]是两个不同的函数；
     2）内层函数u=g(x)的值域一定要在外层函数y=f(u)的定义域内
 


### 不等式基本
   1.加减法法则:对于任意实数 a、b、c，若a<b，则有a+c<b+c和a-c<b-C。这条法则表示不等式两侧加上(或减去)相同的数，不等式的关系保持不变。
   2.乘除法法则:对于任意实数a、b、C (0)，若a<b且c>0，则有ac<bc 和 a/c <b/c。这条法则表示不等式两侧同时乘以 (或除以) 正数，不等式的关系保持不变。
   3.变号法则:对于任意实数 a、b，若a<b且c为负数，则有ac>bc。这条法则表示不等式两侧同时乘以负数，不等式的关系反向改变
   4.平方法则:对于任意实数 a、b，若a<b且a和b均为非负数，则有 a²<b²。这条法则表示两个非负数的平方大小关系与它们本身的大小关系一致。
   5.移项法则:对于任意实数 a、b、c，若a+b<c，则有a<c-b。这条法则表示不等式中，两侧同时加 (或减) 同一个数，不等式的关系保持不变
   

### 判别式法
   代数判别法（▲）和三角判别法[（δ）(读作德尔塔)]。ax²+bx+c=0和三角方程asinx+bcosx=c的根的判别定理。
   来源是二次函数y=x²和三角函数y=sinx的值域。
   1. 代数判别法（▲）
      设f(x)=ax²+bx+c (a≠0),则▲=b²-4ac叫做二次方程f(x)=0或二次函数f(x)的判别式
    






