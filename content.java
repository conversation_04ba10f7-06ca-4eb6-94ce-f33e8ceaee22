package demo.interceptor;
 
import demo.controller.TestController;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
 
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
 
@Configuration
public class MyInterceptor extends WebMvcConfigurerAdapter {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        HandlerInterceptor handlerInterceptor = new HandlerInterceptor() {
             /**
             * 拦截请求的方法
             */
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                request.setCharacterEncoding("utf-8");
                response.setCharacterEncoding("utf-8");
                response.setContentType("text/html;charset=utf-8");
                String name = request.getParameter("name");
                String passwd = request.getParameter("passwd");
                if (name.equals("admin")&&passwd.equals("123")){
                    return true;
                }else{
                    return false;
                }
                //true=放行；false=拦截（直接大白页）
            }
        };
        registry.addInterceptor(handlerInterceptor).addPathPatterns("/**");
    }
}