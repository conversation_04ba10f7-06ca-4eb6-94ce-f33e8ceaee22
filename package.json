{"name": "vite-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.4.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.3.8", "gsap": "^3.12.5", "html5-qrcode": "^2.3.8", "mitt": "^3.0.1", "pinia": "^2.1.6", "sortablejs": "^1.15.6", "vant": "^4.6.5", "vue": "^3.3.4", "vue-baidu-map-3x": "^1.0.39", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.2.4", "vuedraggable": "^2.24.3"}, "devDependencies": {"@iconify-json/ep": "^1.2.2", "@types/node": "^20.5.0", "@vitejs/plugin-vue": "^4.2.3", "sass": "^1.77.8", "sass-loader": "^14.2.1", "typescript": "^5.0.2", "unplugin-auto-import": "^19.2.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.5.0", "vite": "^4.4.5", "vue-tsc": "^1.8.5"}}