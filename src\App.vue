<template>
  <div>
    <el-config-provider :locale="zhCn">
      <Loading ref="loading" />
      <router-view />
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import Loading from './components/loading.vue';
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 创建引用
const loading = ref<InstanceType<typeof Loading> | null>(null);

// 检查加载状态
const checkLoading = () => {
  const timer = setInterval(() => {
    if (document.readyState === 'complete') {
      clearInterval(timer);
      loading.value?.out();
    }
  }, 300);
};

// 设置路由守卫
const router = useRouter();
onMounted(() => {
  checkLoading();
  router.beforeEach((to, from, next) => {
    loading.value?.in(next);
  });
  router.afterEach(() => {
    loading.value?.out();
  });
});
</script>

<style>
* {
  padding: 0;
  margin: 0;
}
</style>