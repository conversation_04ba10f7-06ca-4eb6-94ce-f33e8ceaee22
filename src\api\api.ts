import axios from 'axios';
import router from '../router';

// 创建 Axios 实例
const service = axios.create({
    baseURL: 'http://106.52.248.72:8023',
    timeout: 5000,
    // 添加请求头
    headers: {
        'Content-Type': 'application/json'
    }
});


//  resquest请求拦截器
service.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('Authorization');
        console.log('request', token);
        // 判断token是否存在  再继续下一步配置
        config.headers.Authorization = token;
        return config;
    },
    (error) => {
        // 处理请求错误
        router.replace({
            path: '/login',
        });
        return Promise.reject(error);
    }
);


//  responese响应拦截器
service.interceptors.response.use(
    (response) => {
        // response里有状态值/文本/heads/config/resqest里的一些方法/data

        // 判断response.data是否存在，如果存在，将res值赋值给response.data
        const res = response.data ?? response
        if (response.status != 200) {
            return Promise.reject(new Error('error'));
        } else {
            localStorage.setItem('Authorization', response.headers.authenticator);
            return res;
        }
    },
    (error) => {
        // 超出2xx范围的状态码都会触发该函数
        if (error.response.status === 400) {
            router.replace({
                path: '/login',
            });
        } else {
            // 处理响应错误
            return Promise.reject(error);
        }

    }
);


/**
 * 发送get请求
 * @param url 请求的路径
 * @param params 请求参数
 */
export function get(url: string, params: any) {
    return service({
        url: url,
        method: 'get',
        params: params
    })
}

/**
 * 发送post请求
 * @param url 请求的路径
 * @param data 请求数据
 */
export function post(url: string, data: any) {
    return service({
        url: url,
        method: 'post',
        data: data
    })
}

/**
 * 发送put请求
 * @param url 请求的路径
 * @param data 请求数据
 */
export function put(url: string, data: any) {
    return service({
        url: url,
        method: 'put',
        data: data
    })
}

/**
 * 发送delete请求
 * @param url 请求的路径
 */
export function del(url: string) {
    return service({
        url: url,
        method: 'delete'
    })
}

export default {
    get,
    post,
    put,
    del,
};