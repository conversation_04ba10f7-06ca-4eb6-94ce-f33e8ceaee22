<template>
  <div class="card">
    <h3>{{ msg }}</h3>
    <button type="button" @click="count++">count点击： {{ count }}</button>
    <p>路由组件<code>components/HelloWorld.vue</code>进入组件</p>
  </div>
</template>
<script lang="ts">
import { ref } from "vue";
export default {
  props: {
    msg: String
  },
  setup() {
    const count = ref(0);


    return {
      count
    }
  }
}
</script>
<style scoped ></style >

