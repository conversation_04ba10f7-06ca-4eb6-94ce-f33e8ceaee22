<template>
  <div class="map-container">
    <div class="app-container">
      <div class="search-container">
        <el-form class="search-form" ref="queryFormRef" :model="queryParams" label-position="left" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="GPS设备ID" prop="deviceId">
                <el-input v-model="queryParams.deviceId" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="GPS设备编号" prop="deviceNo">
                <el-input v-model="queryParams.deviceNo" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="GPS最新位置" prop="gpsLastLocation">
                <el-input v-model="queryParams.gpsLastLocation" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="航司日期" prop="gpsTime">
                <el-date-picker v-model="queryParams.gpsTime" type="daterange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="text-right">
              <el-form-item>
                <el-button class="filter-item" type="primary">
                  <i-ep-search />
                  搜索
                </el-button>
                <el-button @click="resetQuery"> <i-ep-refresh />重置 </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <baidu-map class="bm-view" scroll-wheel-zoom :zoom=5 :center="{ lng: 116.404, lat: 39.915 }">
      <bm-marker v-for="(item, index) in positions" :key="index" :position="{ lng: item.lng, lat: item.lat }"
        :icon="{ url: getMarkerIcon(index), size: { width: index === 0 || index === positions.length - 1 ? 32 : 24, height: index === 0 || index === positions.length - 1 ? 32 : 24 } }"
        @click="currentMarker = index">
        <bm-label :content="item.gpsTime" :offset="{ width: -65, height: 10 }" />
        <bm-info-window :show="currentMarker === index" :title="item.sn" style="padding: 0 0 10px 4px">
          <div>
            <div>运单号：{{ item.waybillNumber }}</div>
            <div>当前位置：{{ item.address }}</div>
            <div>经度：{{ item.lng }}</div>
            <div>纬度：{{ item.lat }}</div>
            <div>渠道名称：{{ item.alias }}</div>
            <div>上报时间：{{ item.gpsTime }}</div>
            <el-progress :text-inside="true" striped striped-flow :duration=10 :color="colors" :stroke-width=22
              :percentage="item.electricity" status="warning" />
          </div>
        </bm-info-window>
      </bm-marker>

      <!-- 折线 -->
      <bm-polyline :path="polylinePath" :strokeColor="'#0000ff'" :strokeWeight="5" :strokeOpacity="0.5"></bm-polyline>
    </baidu-map>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, reactive } from 'vue';
import axios from "axios";

const token = ref('eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMzEzIiwibmFtZSI6Iua1i-ivlTIiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL3VzZXJkYXRhIjoie1wiVXNlcklkXCI6MzEzLFwiRGVwdElkXCI6MjE3LFwiVXNlck5hbWVcIjpcIua1i-ivlTJcIixcIlJvbGVJZFwiOjExOSxcIlVzZXJUeXBlXCI6XCIxXCIsXCJQZXJtaXNzaW9uc1wiOltdLFwiTG9naW5UeXBlXCI6bnVsbH0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MjExMDIyNzAsImV4cCI6MTcyMTE4ODY3MCwiaWF0IjoxNzIxMTAyMjcwLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.iT7T5KuL9LNFTKrepI4QHM5B0VwE7L7_pOvzuvjei_Y')
localStorage.setItem('token', token.value);



// 获取数据
const apiList = () => {
  const token = localStorage.getItem('token');

  const config = {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  }
  console.log(token, '=======');
  const res = axios.get('http://************3:8888/Api/getDevices', config);
  console.log('数据获取成功：', res);
}

// 重置
const queryFormRef = ref();
function resetQuery() {
  queryFormRef.value.resetFields();
}


// 电量区间颜色
const colors = [
  { color: '#B22222', percentage: 20 },
  { color: '#F4A460', percentage: 40 },
  { color: '#00E5EE', percentage: 60 },
  { color: '#4876FF', percentage: 80 },
  { color: '#00EE76', percentage: 100 },
]

const queryParams = reactive<any>({
  preOutTime: null
})
const positions = [
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-23 05:56:04', sn: '14091216530', lat: 23.405, lng: 113.271, electricity: 98, address: '湖南省衡阳市衡东县霞流镇汤家垅;衡山服务区与G4京港澳高速路口南492米' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-23 03:56:17', sn: '14091216530', lat: 23.806, lng: 113.211, electricity: 88, address: '湖南省株洲市天元区太高水库环线' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 23:55:22', sn: '14091216530', lat: 24.418, lng: 111.580, electricity: 73, address: '湖南省岳阳市岳阳县' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 21:56:24', sn: '14091216530', lat: 25.484, lng: 111.565, electricity: 64, address: '湖北省荆州市监利县黄歇口镇东门台' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 19:57:28', sn: '14091216530', lat: 26.151, lng: 109.859, electricity: 54, address: '北省荆门市沙洋县五里铺镇严店村;得胜街与207国道路口东南747米' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 17:58:40', sn: '14091216530', lat: 26.681, lng: 106.614, electricity: 49, address: '湖北省襄阳市襄城区欧庙镇胡家凹' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 15:59:26', sn: '14091216530', lat: 26.681, lng: 104.614, electricity: 33, address: '北省十堰市丹江口市武当山街道压火寨;G70福银高速与武当山服务区路口北411米「new」' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2024-07-12 12:33:41', sn: '14091216530', lat: 27.681, lng: 102.614, electricity: 28, address: '陕西省商洛市山阳县城关镇河西;G70福银高速与山阳服务区路口南250米' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2022-04-22 14:00:41', sn: '14091216530', lat: 28.503, lng: 100.484, electricity: 19, address: '陕西省咸阳市渭城区X234' },
  { alias: '欧洲卡航', waybillNumber: 'OK3187', gpsTime: '2024-07-05 04:11:18', sn: '14091216530', lat: 45.345, lng: 82.637, electricity: 19, address: ' 新疆维吾尔自治区塔城地区托里县S318' },
]
const polylinePath = computed(() => positions)
const currentMarker = ref<number | null>(null)

const getMarkerIcon = (index: number) => {
  if (index === 0) {
    return 'https://www.umbrella-holder.top:9090/blog/aurora/photos/8475fb4fb33892fc164a836379456f99.png'
  } else if (index === positions.length - 1) {
    return 'https://www.umbrella-holder.top:9090/blog/aurora/photos/3a02726a4d8664118b1c579db20fbaa8.png'
  } else {
    return 'https://www.umbrella-holder.top:9090/blog/aurora/photos/9957fe8ed4a5ca316053676f752f003c.png'

  }
};
onMounted(() => {
  apiList()
})
</script>

<style lang="scss">
body,
html {
  padding: 0;
  margin: 0;
  height: 100%;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.bm-view {
  width: 100%;
  height: 100%;
}

.BMapLabel {
  font-size: 14px !important;
  border: 0 !important;
  background-color: transparent !important;
  font-weight: bold
}


.app-container {
  padding: 10px;
}

.search-container {
  padding: 18px 0 0 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  box-shadow: var(--el-box-shadow-light);
}

.search-form {
  padding: 0 20px;
}
</style>