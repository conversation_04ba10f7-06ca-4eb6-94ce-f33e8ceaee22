<template>
    <div class="post-list">
        <div v-for="post in posts" :key="post.id">

        </div>
    </div>
</template>
<script lang="ts">
import { ref, onMounted, onUnmounted, onUpdated } from "vue";
export default {
    setup() {
        const posts = ref([
            { title: "标题1", body: "vue1", id: 1 },
            { title: "标题2", body: "lore200ddddd22222222222333", id: 2 },
            { title: "标题3", body: "vue3", id: 3 },
            { title: "标题4", body: "vue4", id: 4 },
        ]); const showPost = ref(true);
        onMounted(() => {
            // alert("页面渲染之前执行，执行完，页面就出来了");
        })
        //页面执行之前既可以用onMounted也可以用watchEffect
        //watchEffect();
        onUnmounted(() => {
            alert("组件注销之前执行，执行完组件就不在页面显示了");
        })
        onUpdated(() => {
            alert("当组件内的内容发生变化时，就会执行这个更新钩子函数");
        })
        return { posts,showPost }
    }
}
</script>

<style scoped></style>