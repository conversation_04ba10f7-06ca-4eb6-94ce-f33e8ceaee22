<template>
    <div class="borderRadio">确认阶段</div>
    <div>
        <div class="footer">
            <div class="border">原型中得方块</div>
        </div>
        <section>
            <option value="1">111</option>
            <option value="2">222</option>
            <option value="3">333</option>
        </section>
    </div>
</template>

<script lang="ts">
export default {
    setup() {
        return {
        }
    }
}
</script>

<style  scoped>
.footer {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background-color: wheat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.borderRadio {
    width: 80px;
    height: 80px;
    background-color: rgb(136, 255, 0);
    line-height: 80px;
    border-radius: 50%;
    /* border-style: solid; */
    border: 4px solid #fff;
    /* box-shadow: 10px 0px 0px 0px; */
    border-bottom-color: #ff0000;
    border-left-color: #0e7a0e;
    border-right-color: #c2109b;
    border-top-color: #98e40a;
}

.border {
    width: 100px;
    height: 100px;
    background-color: aquamarine;
    position: relative;
    height: 100px;
    text-align: center;
}
</style>