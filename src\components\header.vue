<template>
    <div>
        <div class="header">
            <li v-for="site in sites">
                {{ site.text }}
            </li>
        </div>
    </div>
</template>

<script lang="ts">
import { ref } from "vue";
export default {
    setup() {
        const sites: any = ref([
            { text: '<PERSON><PERSON>' },
            { text: 'Runoob' },
            { text: 'Taobao' },
            { text: 'jingdo' }
        ])


        let type: number[] = [1, 2, 3]
        let typeNumber: Array<string> = ['a', 'b', 'c']

        function MyFn(a = 10, b: string, c?: boolean, ...rest: number[]) {
            100
            a;
            b;
            c;
            rest
        }

        interface Obj {
            name: string,
            age: number
        }
        const obj: Obj = {

            name: 'a',
            age: 18,
        }

        const datavalue: any =
            [
                { name: "dara", },
                { screen: "doniw", },
                { name: "crenn", },
                { name: "drak", },
            ]

        return {
            ref,
            sites,
            obj,
            type,
            datavalue,
            typeNumber,
            MyFn,
        }
    }
}
</script>

<style scoped>
.header {
    margin: 0 auto;
    padding: 30px;
    background-color: burlywood;
}
</style>