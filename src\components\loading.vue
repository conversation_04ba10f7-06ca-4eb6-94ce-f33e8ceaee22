<template>
    <div id="loading">
        <svg viewBox="0 0 50 50">
            <circle r="25" cx="25" cy="25"></circle>
        </svg>
        <p>LOADING</p>
    </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';


const inLoading = (next: () => void) => {
    const container = document.getElementById('loading');
    if (container) {
        container.classList.remove('loading_out');
        setTimeout(() => {
            next();
            emit('checkLoading');
        }, 1000);
    }
};

const outLoading = () => {
    const container = document.getElementById('loading');
    if (container) {
        container.classList.add('loading_out');
    }
};
const emit = defineEmits<{
    (e: 'checkLoading'): void;
}>();

defineExpose({ in: inLoading, out: outLoading });
</script>

<style scoped>
* {
    padding: 0;
    margin: 0;
    font-size: 2vmin;
}

div {
    display: flex;
    justify-content: center;
    align-items: center;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
}

#loading {
    position: fixed;
    flex-direction: column;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: #f7f7f7;
    z-index: 100000000;
    transition: 1s ease;
}

#loading svg {
    width: 5rem;
    margin-bottom: 2rem;
    overflow: visible;
    transition: 0.3s ease;
}

#loading svg circle {
    fill: none;
    stroke: #171717;
    stroke-width: 12;
    stroke-dasharray: 160;
    stroke-dashoffset: 160;
    transform-origin: center;
    animation: circle_rotate 3s ease-in infinite;
}

@keyframes circle_rotate {
    0% {
        transform: rotate(0deg);
        stroke-dashoffset: 160;
    }

    100% {
        transform: rotate(360deg);
        stroke-dashoffset: -160;
    }
}

#loading p {
    font-family: sans-serif;
    font-size: 2rem;
    color: #171717;
    font-weight: 900;
    transition: 0.3s ease;
}

.loading_out {
    transform: translateY(100%);
}

.loading_out svg,
.loading_out p {
    opacity: 0;
}
</style>