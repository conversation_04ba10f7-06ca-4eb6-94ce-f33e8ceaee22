<template>
    <el-drawer v-model="dialogVisiable" @closed="closeDialog" :title="title" size="60%">
        <el-form :model="form" label-width="auto" style="max-width: 600px">
            <el-form-item label="姓名">
                <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="专业">
                <el-select v-model="form.region" placeholder="请选择类型">
                    <el-option label="土木" value="shanghai" />
                    <el-option label="设计" value="beijing" />
                </el-select>
            </el-form-item>
            <el-form-item label="时间">
                <el-col :span="11">
                    <el-date-picker v-model="form.date1" type="date" placeholder="选择时间" style="width: 100%" />
                </el-col>
                <el-col :span="2" class="text-center">
                    <span class="text-gray-500">-</span>
                </el-col>
            </el-form-item>
            <el-form-item label="是否调剂">
                <el-switch v-model="form.delivery" />
            </el-form-item>
            <el-form-item label="城市">
                <el-checkbox-group v-model="form.checkedCities">
                    <el-checkbox :label="1">北京</el-checkbox>
                    <el-checkbox :label="2">上海</el-checkbox>
                    <el-checkbox :label="3">广州</el-checkbox>
                    <el-checkbox :label="4">深圳</el-checkbox>
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="主修科目">
                <el-radio-group v-model="form.resource">
                    <el-radio :label="1">市场营销</el-radio>
                    <el-radio :label="2">物流金融</el-radio>
                    <el-radio :label="3">土木建设</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注">
                <el-input v-model="form.desc" type="textarea" />
            </el-form-item>
            <el-form-item>
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="onSubmit">确认</el-button>
            </el-form-item>
        </el-form>
    </el-drawer>
</template>

<script lang="ts" setup>
import { toRefs } from 'vue'

const props = defineProps({
    title: String,
    dialogVisiable: Boolean,
    name: String,
    form: Object
});


const emits = defineEmits(["close"]);
const { title, dialogVisiable, form } = toRefs(props);
console.log('form', form?.value);

const onSubmit = () => {
    
    console.log('submit!')
}

const closeDialog = () => {
    dialogVisiable.value = false;
    emits("close");
};
</script>