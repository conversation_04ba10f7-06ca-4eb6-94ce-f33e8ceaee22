<!-- <template>
    <div class="qrcode">
        <div id="reader"></div>
    </div>
</template>
  
<script>
import { Html5Qrcode } from 'html5-qrcode';
export default {
    created() {
        this.getCameras();
    },
    beforeUnmount() {
        this.stop();
    },
    methods: {
        getCameras() {
            Html5Qrcode.getCameras()
                .then((devices) => {
                    if (devices && devices.length) {
                        this.html5QrCode = new Html5Qrcode('reader');
                        this.start();
                    }
                })
                .catch((err) => {
                    // handle err
                    this.html5QrCode = new Html5Qrcode('reader');
                    this.error = 'ERROR: 您需要授予相机访问权限';
                    this.$emit('err', this.error, err);
                });
        },
        start() {
            //environment后置 user前置
            this.html5QrCode
                .start(
                    { facingMode: 'environment' },
                    {
                        fps: 2,    // 设置每秒多少帧
                        qrbox: { width: 250, height: 250 },  // 设置取景范围
                    },
                    (decodedText) => {
                        this.$emit('ok', decodedText);
                    },
                )
                .catch((err) => {
                    this.$emit('err', err);
                });
        },
        stop() {
            this.html5QrCode
                .stop()
                .then((ignore) => {
                    // QR Code scanning is stopped.
                    this.$emit('err', ignore);
                    console.log('QR Code scanning stopped.');
                })
                .catch((err) => {
                    this.$emit('err', err);
                    // Stop failed, handle it.
                    console.log('Unable to stop scanning.');
                });
        },
    },
};
</script>
  
<style  scoped>
.qrcode {
    position: relative;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
}

#reader {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}
</style>
  
   -->















   <template>
	<el-dialog v-if="dialogVisiable" v-model="dialogVisiable" :title="title" width="75%">
		<!-- <el-form ref="channelAddFormRef" class="form-default" :model="formData" label-width="150px">
            <div class="dialog-sub-title">运单信息：</div>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="客户" prop="custId">
                        <el-select v-model.number="formData.shipment.custId" filterable clearable @change="custChange">
                            <el-option v-for="item in custList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户单号" prop="referenceNo">
                        <el-input v-model="formData.shipment.referenceNo" maxlength="30" placeholder="请输入" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="国家" prop="shipment.countryCode">
                        <el-select v-model="formData.shipment.countryCode" filterable clearable @change="countryChange">
                        <el-option v-for="item in countryDict" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="运输方式" prop="transportWay" >
                        <el-select v-model="formData.shipment.transportWay" clearable @change="transportWayChange">
                            <el-option v-for="item in dictData.transportWay" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="渠道" prop="channelCode">
                        <el-select v-model="formData.shipment.channelCode" clearable @change="channelChange">
                            <el-option v-for="item in channelList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="交货仓库" prop="warehouse">
                        <el-select v-model.number="formData.shipment.warehouse" clearable>
                            <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="报关方式" prop="customsDeclaration">
                        <el-radio-group v-model="formData.shipment.customsDeclaration" clearable>
                            <el-radio v-for="item in dictData.baoguanWay" :key="item.value" :label="item.value">{{
                                item.label
                            }}</el-radio>
                            <el-radio v-if="formData.shipment.customsDeclaration" label="">无</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="清关方式" prop="clearCustom">
                        <el-radio-group v-model="formData.shipment.clearCustom" clearable>
                            <el-radio v-for="item in dictData.qingguanWay" :key="item.value" :label="item.value">{{
                                item.label
                            }}</el-radio>
                            <el-radio v-if="formData.shipment.clearCustom" label="">无</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="物品属性" prop="goodsAttr">
                        <el-select v-model="formData.shipment.goodsAttr" multiple clearable>
                            <el-option v-for="item in goodsAttrDict" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="formData.shipment.remark" type="textarea" :row="4" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="内部备注" prop="remarkInner">
                        <el-input v-model="formData.shipment.remarkInner" type="textarea" :row="4" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="件数" prop="paperNum">
                        <el-input v-model.number="formData.paperNum" type="number" maxlength="30" placeholder="请输入" />
                    </el-form-item>
                </el-col>
				<el-col :span="8">
                    <el-form-item label="收货时间" prop="receiptTime">
                        <el-date-picker style="width:100%;" v-model="formData.shipment.receiptTime" type="datetime"
                            placeholder="请选择时间" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                </el-col>
            </el-row>
            <div v-if="formData.shipment.clearCustom === '2' || formData.shipment.clearCustom === '3'"
                class="dialog-sub-title">VAT信息：</div>
            <el-row v-if="formData.shipment.clearCustom == '2' || formData.shipment.clearCustom === '3'">
                <el-col :span="10">
                    <el-form-item label="EORI号码" prop="vatEoriNo">
                        <el-input v-model="formData.shipment.vatEoriNo" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="VAT英文公司名称" prop="vatEnConpanyName">
                        <el-input v-model="formData.shipment.vatEnConpanyName" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="VAT注册地址" prop="vatRegisterAddress">
                        <el-input v-model="formData.shipment.vatRegisterAddress" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="联系姓名" prop="vatContactName">
                        <el-input v-model="formData.shipment.vatContactName" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="联系电话" prop="vatContactMobile">
                        <el-input v-model="formData.shipment.vatContactMobile" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="AEO企业编码" prop="vatAeoCode">
                        <el-input v-model="formData.shipment.vatAeoCode" type="text" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="备注" prop="vatRemark">
                        <el-input v-model="formData.shipment.vatRemark" type="textarea" :row="4" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> -->
		<div class="mb2 flex">
			<el-input v-model.number="addRowCount" class="width100" maxlength="3" type="number"
				style="width: 60px; text-align: center" />
			<el-link class="mr5" :underline="false" type="primary" @click="addRow(addRowCount)">+ 新增</el-link>
		</div>
		<div class="flex-container">
			<div class="excel-container">
				<div id="spreadsheetFeePayId" ref="spreadsheetFeePay"></div>
			</div>
			<!-- <el-table class="sum-table" :data="paperDataCommon" border>
				<el-table-column prop="volumeWeight" label="材积重" width="100" />
				<el-table-column prop="volume" label="方数" width="100" />
			</el-table> -->
		</div>
		<div v-if="totalData" class="flex">
			<div>总重量： <span class="total-data">{{ toFixed2(totalData.weight) }}</span></div>
			<div>总材积重： <span class="total-data">{{ toFixed2(totalData.volumeWeight) }}</span></div>
			<div>总计费重： <span class="total-data">{{ toFixed2(totalData.billWeight) }}</span></div>
			<div>总方数： <span class="total-data">{{ toFixed2(totalData.volume) }}</span></div>
		</div>
		<div v-if="errorNumList.length > 0" class="pt2">
			箱号<span v-for="item in errorNumList" :key="item" class="color-red">{{ item }}，</span>在系统找不到,请重新录入
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button v-if="saveShow" type="primary" @click="onSave()"> 确 认 </el-button>
				<el-button @click="closeDialog"> 关 闭 </el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup lang="ts">
defineOptions({
	name: "SizeInputBatch",
});
import orderApi from "@/api/order/orderBig";
import addressApi from "@/api/setting/address";
import warehouseApi from "@/api/setting/warehouse";
import { getClientDetail } from "@/api/user";
import dictData from "@/enums/dict";
import type { FormInstance, FormRules } from 'element-plus'
import orderReviewApi from "@/api/bill/orderReview";
import jexcel from "jspreadsheet-ce";
import "jspreadsheet-ce/dist/jspreadsheet.css";
import { GetVolume, GetVolumeWeight, GetSizeTotal } from "@/hooks/getGoodsName";
import { toFixed2 } from "@/hooks/utils";
import useCommonData from "@/hooks/useCommonData";
import useDict, { DictItem } from "@/hooks/useDict";
import { useUserStore } from "@/store/modules/user";
import dayjs from "dayjs";
import { hideLoading, showLoading } from "@/hooks/loading";
// const { getDictListByCodes, getLabel } = useDict();
const userStore = useUserStore();
const props = defineProps({
	//子组件接收父组件传递过来的值
	dialogVisiable: Boolean,
	title: String,
	saveShow: Number
});
const { title } = toRefs(props);
const saveShow = ref(1)
const dialogVisiable = ref(false);
// const channelAddFormRef = ref(ElForm);
const emits = defineEmits(["close"]);
const addRowCount = ref(1);
const shipmentStatus = ref(1)
const shipmentId = ref(null)
const shipmentNo = ref("")
const initData = (id: any, no: string, btnShow: number, status = 1) => {
	paperDataCommon.value = [];
	saveShow.value = btnShow
	dialogVisiable.value = true;
	shipmentStatus.value = status;
	shipmentId.value = id;
	shipmentNo.value = no;
	setTimeout(() => {
		if (spreadsheetFeePay.value) {
			initSpreadsheet(spreadsheetFeePay.value);
			getDetail(id, no);
		}
	}, 100);
}

const formDataRef = ref();

const sizeColumns = computed(() => {
	return [
		{
			type: "text",
			title: "箱号",
			width: "0px",
			required: true,
			prop: "item_number",
		},
		{
			type: "text",
			title: "箱号",
			width: "120px",
			required: true,
			prop: "item_number_box",
		},
		{
			type: "text",
			title: "FBA箱号",
			width: "120px",
			required: false,
			prop: "item_number_fba",
		},
		{
			type: "text",
			title: "重量（KG）",
			width: "100px",
			required: false,
			prop: "weight",
		},
		{
			type: "text",
			title: "长（CM）",
			width: "100px",
			required: false,
			prop: "length",
		},
		{
			type: "text",
			title: "宽（CM）",
			width: "100px",
			required: false,
			prop: "width",
		},
		{
			type: "text",
			title: "高（CM）",
			width: "100px",
			required: false,
			prop: "height"
		}
		// {
		// 	type: "text",
		// 	title: "材积重",
		// 	width: "100px",
		// 	required: false,
		// 	prop: "volumeWeight"
		// },
		// {
		// 	type: "text",
		// 	title: "方数",
		// 	width: "100px",
		// 	required: false,
		// 	prop: "volume"
		// }
	]
});


// 表格相关
const spreadsheetFeePay = ref<HTMLElement | null>(null);
const spreadsheet = ref();
const timerbox = ref<any>(null);
const changed = (instance, cell, x, y, value) => {
	if (timerbox.value) {
		clearTimeout(timerbox.value);
	}
	timerbox.value = setTimeout(() => {
		getTableData()
	}, 2000);
	/* let data = spreadsheet.value.getData();
	data[y][x] = res.data.url;
	spreadsheet.value.setData(data); */
};

const initSpreadsheet = (container: HTMLElement) => {
	// 创建 jspreadsheet 实例
	spreadsheet.value = jexcel(container, {
		allowToolbar: true,
		data: [["xxxx001", "", "", "", ""]],
		columns: sizeColumns.value,
		updateTable: function (el, cell, x, y, source, value, id) {

		},
		onchange: changed,
	});
	sizeColumns.value.forEach((item: any, index: number) => {
		if (item.required) {
			spreadsheet.value.headers[index].innerHTML = `<span >${item.title} <span style="color: red;">*</span></span>`
		}
	})
	// 可以在这里添加其他自定义配置或事件处理程序
};
const addRow = (num = 1) => {
	for (let i = 0; i < num; i++) {
		spreadsheet.value.insertRow();
	}
};
const totalData = ref<any>()
const paperDataCommon = ref<any>(null)
const getTableData = () => {
	const data = spreadsheet.value.getData();
	console.log("data", data);
	let papers = data.map((item: any) => {
		return {
			boxNumber: item[1],
			item_number: item[1],
			extendOrderNo: item[2],
			itemsWeight: item[3],
			itemsLength: item[4],
			itemsWidth: item[5],
			itemsHeight: item[6]
		};
	});
	let paperData = papers.map((item: any) => {
		let volumeWeight = GetVolumeWeight(item, bubbleCoeffcient.value)
		let volume = GetVolume(item)
		return [
			item.boxNumber,
			item.boxNumber,
			item.extendOrderNo || "",
			item.itemsWeight,
			item.itemsLength,
			item.itemsWidth,
			item.itemsHeight,
			volumeWeight,
			volume
		]
	});
	let paperDataCommonDefault = papers.map((item: any) => {
		let volumeWeight = GetVolumeWeight(item, bubbleCoeffcient.value)
		let volume = GetVolume(item)
		return {
			volumeWeight,
			volume
		}
	});
	/* if (papers[0].extendOrderNo) {
		spreadsheet.value.setWidth(1, '0px')
	} else {
		spreadsheet.value.setWidth(2, '0px')
	} */
	console.log(paperData);
	paperDataCommon.value = [];
	paperDataCommon.value = paperDataCommonDefault;
	console.log(paperDataCommonDefault)
	// spreadsheet.value.setData(paperData);
	totalData.value = GetSizeTotal(papers)
	console.log("totalData", totalData.value)
	return papers;
};

const errorNumList = ref<string[]>([])
const collectionTest = async (item) => {
	let params = {
		action: "check",
		item_number: "",
		cc_number: "清关号",
		time: "时间戳",
		token: "验证码"
	}
	params.item_number = item.item_number;
	await orderApi.checkBoxAndPushNew(params).then((res: any) => {
		let data = res;
		console.log(res);
		if (data.status === 1) {
			collectionPush(item)
		} else {
			// errorNumList.value.push(item.item_number);
			// ElMessage.error("箱子不存在")
		}
	}).catch(err => {
		errorNumList.value.push(item.item_number);
	})
}
const collectionPush = (data: any) => {
	let params = {
		action: "pickup",
		cc_number: "清关号",
		pic_url: "",
		time: "时间戳",
		token: "验证码",
		optUserName: userStore.chineseName
	}
	let newParams = data.map(item => {
		return {
			...params,
			...item
		}
	})
	showLoading();
	// let newParams = { ...params, ...item }
	orderApi.setSizeBatchNew(newParams).then(res => {
		ElMessage.success("操作成功")
		if (shipmentStatus.value === 1) {
			setRoute()
		}
		hideLoading();
		closeDialog();
	}).catch(err => {
		console.log(err);
		hideLoading();
	});
}
const orderIds = ref([])
const setRoute = () => {
  let params = {
    shipments: [{
		id: shipmentId.value,
		shipmentNo: shipmentNo.value,
	}],
    routeInfo: {
      operInfo: "已入仓",
      routeTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      routeInfoType: "1"
    }
  }
  orderApi.setRouteBatchAdd(params).then(res => {
    // ElMessage.success("操作成功");
  })
}

const formData = reactive<any>({
	shipment: {
		shipmentType: 1,
		shipmentNo: "",
		// receiptTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
		custId: "",
		custName: "",
		salerId: "",
		custServicerId: "",
		referenceNo: "",
		transportWay: "",
		channelCode: "",
		warehouse: "",
		customsDeclaration: "",
		clearCustom: "",
		goodsAttr: "",
		remark: "",
		remarkInner: "",
		vatNo: "",
		vatEoriNo: "",
		vatEnConpanyName: "",
		vatRegisterAddress: "",
		vatContactName: "",
		vatContactMobile: "",
		vatAeoCode: "",
		vatRemark: "",
		libName: "",
		recipients: "",
		company: "",
		numberPlate: "",
		address1: "",
		address2: "",
		address3: "",
		town: "",
		city: "",
		province: "",
		postcode: "",
		country: "",
		countryCode: "",
		email: "",
		addressRemark: "",
		column1: "",
		column2: "",
		pickinger: "",
	},
	shipmentStatisVo: {},
	shipmentPapers: [],
	routeInfos: [],
	workOrders: []
});
const bubbleCoeffcient = ref(6000)
const shipmentPapers = ref<any>([]);
const getDetail = async (id: number | string, shipmentNo: string) => {
	totalData.value = null;
	shipmentPapers.value = []
	if (id !== "") {
		let params = {
			id,
			shipmentNo
		}
		const { data } = await orderApi.getDetail(params);
		Object.assign(formData, data);
		//resultArray.value = Object.values(groupedData(formData.shipmentPapers));
		shipmentPapers.value = formData.shipmentPapers.filter((paper, index) => {
			const isDuplicate = index > 0 && paper.boxNumber === formData.shipmentPapers[index - 1].boxNumber;
			return !isDuplicate;
		});
		bubbleCoeffcient.value = formData.shipmentStatisVo.bubbleCoeffcient || 6000
		let paperData = shipmentPapers.value.map((item: any) => {
			let volumeWeight = GetVolumeWeight(item, bubbleCoeffcient.value)
			let volume = GetVolume(item)
			return [
				item.boxNumber,
				item.boxNumber,
				item.extendOrderNo || "",
				item.itemsWeight,
				item.itemsLength,
				item.itemsWidth,
				item.itemsHeight,
				volumeWeight,
				volume
			]
		});
		/* if (shipmentPapers.value[0].extendOrderNo) {
			spreadsheet.value.setWidth(1, '0px')
			spreadsheet.value.setWidth(2, '160px')
		} else {
			spreadsheet.value.setWidth(2, '0px')
			spreadsheet.value.setWidth(1, '160px')
		} */
		paperDataCommon.value = paperData;
		totalData.value = GetSizeTotal(shipmentPapers.value)
		spreadsheet.value.setData(paperData);
	}
}

const onSUbmit = () => {

}

// 客户
// const getClientDetailData = async (id: number) => {
//   const { data } = await getClientDetail(id);
// 	formData.shipment.salerId = data.salesman;
// 	formData.shipment.custServicerId = data.custServicer;
// 	bubbleCoeffcient.value = data.bubbleCoeffcient
// 	let params = {
// 		salerId: data.salesman,
// 		custServicerId: data.custServicer
// 	}
// 	return params;
// }
// const custList = ref<any>([]);
// const custChange = (id: number) => {
//     const selectedCust = custList.value.find((item: any) => item.value === id);
//     if (selectedCust) {
//         formData.shipment.custName = selectedCust.id;
//     } else {
//         formData.shipment.custName = "";
//     }
//     getClientDetailData(id);
// }

// // 国家选项列表
// const countryChange = (val: number) => {
//     const selectedCountry = countryDict.value.find((item: any) => item.value === val);
//     formData.shipment.country = selectedCountry?.label;
// }
// const countryDict = ref<DictItem[]>([]);

// // 物品属性
// const goodsAttrDict = ref<DictItem[]>([]);
// 	const getDictData = async () => {
//     const [countryData, goodsAttrData] = await getDictListByCodes([
//         "aimCountry",
//         "goodsAttr"
//     ]);

//     countryDict.value = countryData;
//     goodsAttrDict.value = goodsAttrData;
// };

// // 获取渠道、仓库
// const warehouseList = ref<any>([]);
// const addressList = ref<any>([]);
// const getChannelList = async () => {
// 	let params = {
// 		current: 1,
//         pageSize: 100,
// 		};
// 		let addressParams = {
// 			country: "",
// 			postcode: ""
// 			}
// 			const houseData = await warehouseApi.getList(params);
			
// 			const addrssData = await addressApi.getListAddressAll(addressParams);
// 			warehouseList.value = houseData.data.records;
// 			addressList.value = addrssData.data;
// };

// // 渠道
// const channelList = ref<any>([]);
// const channelChange = (code: string) => {
//     console.log(code);
//   let channelItem = channelList.value.find((item: any) => item.value === code)
//   formData.shipment.transportWay = channelItem.transportWay;
//     /* channelApi.getSupplierTypeByCode(code).then(res => {
// 		currentChannel.value = res.data;
// 		console.log("res", res)
// 	}) */
// };

// // 运输方式
// const transportWayChange = (way: string) => {
//     console.log(way)
//     if (!way) {
//         channelList.value = channelListCopy.value
//     } else {
//         formData.shipment.channelCode = ""
//         channelList.value = channelListCopy.value.filter((item: any) => item.transportWay === way)
//     }
// }


const onSave = async () => {

// 	// 表单提交
// 	console.log(formData);
//     channelAddFormRef.value.validate(async (valid: any) => {
//         if (valid) {
//             if (formData.shipment.custId) {
//                 await custChange(formData.shipment.custId)
//             }
//             let data = JSON.parse(JSON.stringify(formData));
			
//             // if (data.shipment.goodsAttr && data.shipment.goodsAttr.length > 0) {
//             //     data.shipment.goodsAttr = data.shipment.goodsAttr.join(",");
//             // } else {
// 			// 	data.shipment.goodsAttr = "";
// 			// }
//             console.log("data", data);
// 			let newData = {
//                 // shipment: data.shipment,
// 				// shipment: {
// 					custId: formData.shipment.custId,
// 					custName: formData.shipment.custName,
// 					referenceNo: formData.shipment.referenceNo,
// 					clearCustom: formData.shipment.clearCustom,
// 					customsDeclaration: formData.shipment.customsDeclaration,
// 					transportWay: formData.shipment.transportWay,
// 					warehouse: formData.shipment.warehouse,
// 					countryCode: formData.shipment.countryCode,
// 					channelCode: formData.shipment.channelCode,
// 					goodsAttr: formData.shipment.goodsAttr,
// 					remark: formData.shipment.remark,
// 					remarkInner: formData.shipment.remarkInner,
// 					receiptTime: formData.shipment.receiptTime,
// 				// },
// 				// paperNum: formData.paperNum
// 			}
//             showLoading();
// 			orderApi.updateOrderBatch(newData).then(() => {
// 				ElMessage.success("操作成功");
// 				closeDialog();
// 			}).finally(() => (
//                 hideLoading()
//             ));
//         }
//     });


	console.log("tableData", getTableData());
	let sizeData = getTableData();
	console.log("signData", sizeData);
	
	let data = sizeData.map(item => {
		return {
			boxNumber: item.boxNumber,
			shipmentNo: formData.shipment.shipmentNo,
			boxNo: parseInt(item.boxNumber.slice(-3)),
			extendOrderNo: item.extendOrderNo,
			itemsWeight: Number(item.itemsWeight),
			itemsLength: Number(item.itemsLength),
			itemsWidth: Number(item.itemsWidth),
			itemsHeight: Number(item.itemsHeight)
		}
	})
	console.log(data);
	let newData = data.filter(item => item.boxNumber && item.itemsWeight && item.itemsLength && item.itemsWidth && item.itemsHeight)
	
	console.log("data", data);
	console.log("data", newData);
	if (newData.length <= 0) {
		return;
	}
	collectionPush(newData)
	errorNumList.value = [];
	/* let promises = []; // 创建一个空数组来存储所有的异步请求
	console.log(data);
	promises.push();  */
	/* for (let i = 0; i < data.length; i++) {
		console.log("data", data[i])
		console.log("data", shipmentPapers.value[i])
		if (data[i].item_number && data[i].weight && data[i].length && data[i].width && data[i].height) {
			promises.push(collectionPush(data[i])); // 将每个异步请求添加到数组中		
		}
	} */
	/* Promise.all(promises).then(responses => { // 等待所有的异步请求完成
		console.log(333);
		console.log("errorNumList1", errorNumList.value);
		setTimeout(() => {
			if (errorNumList.value.length === 0) {
				ElMessage.success("操作成功")
				closeDialog();
			}
		}, 2000)

	}).catch(error => {
		if (errorNumList.value.length === 0) {
			// closeDialog();
		}
		console.log("errorNumList2", errorNumList.value);
		console.log(error)
	}) */
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
	dialogVisiable.value = false;
	const container = document.getElementById('spreadsheetFeePayId');
	if (container) {
		container.innerHTML = "";
	}
	emits("close");
};

const { GetCustData,GetChannelData } = useCommonData();
const channelListCopy = ref<any>([]);
onMounted(async () => {
	// getDictData()
    // getChannelList();
    // custList.value = await GetCustData();
    // channelList.value = await GetChannelData();
    // channelList.value = channelList.value.filter((item: any) => item.busiType === '1' && item.status === 1);
    // channelListCopy.value = channelList.value.filter((item: any) => item.busiType === '1' && item.status === 1);
});
defineExpose({ initData, onSave })
</script>
<style lang="scss" scoped>
.color-item {
	padding: 6px;
	display: inline-block;
	border-radius: 6px;
}
.total-data {
	font-weight: bold;
	font-size: 16px;
	padding-right: 20px;
}
.flex-container {
	display: flex;
}
/* .sum-table {
	width: 200px;
}
.excel-container {
	width: 600px;
} */
</style>
<style lang="scss">
:deep(.sum-table .el-table--default .el-table__cell) {
	padding: 0;
}
</style>
    
