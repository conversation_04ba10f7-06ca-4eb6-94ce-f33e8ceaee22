import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import axios from 'axios'
import router from './router'
import elementPlus from 'element-plus'
import { Button, Toast } from "vant";
import { createPinia } from 'pinia'

// 国际化
// import { createI18n } from 'vue-i18n';
// import en from './locales/en.json'; 
// import zh from './locales/zh.json'; 
// const i18n = createI18n({
//   locale: 'en', // 设置默认语言
//   messages: {
//     en,
//     zh,
//   },
// });

import 'element-plus/dist/index.css'
import 'vant/lib/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

axios.defaults.baseURL = "/Api"
const pinia = createPinia()
const app = createApp(App)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(router)
    .use(pinia)
    .use(elementPlus)
    // .use(i18n)
    .use(Button)
    .use(Toast)
    .mount('#app')