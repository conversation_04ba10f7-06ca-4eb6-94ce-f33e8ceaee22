
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
    { path: '/header', name: 'header', component: () => import('../components/header.vue') },
    { path: '/footer', name: 'footer', component: () => import('../components/footer.vue') },
    { path: '/MsMap', name: 'MsMap', component: () => import('../components/MsMap.vue') },
    { path: '/qrcode', name: 'qrcode', component: () => import('../components/qrcode.vue') },
    { path: '/echars', name: 'echars', component: () => import('../components/echars.vue') },
    { path: '/home', name: 'home', component: () => import('../views/home.vue') },
    { path: '/bar', name: 'bar', component: () => import('../views/bar.vue') },
    { path: '/page', name: 'page', component: () => import('../views/page.vue') },
    { path: '/', name: 'login', component: () => import('../views/login.vue') },
    { path: '/index', name: 'index', component: () => import('../views/index.vue') },
    { path: '/table', name: 'table', component: () => import('../views/table.vue') },
    { path: '/list', name: 'list', component: () => import('../views/list.vue') },
    { path: '/photos', name: 'photos', component: () => import('../views/photos.vue') },
    { path: '/canvas', name: 'canvas', component: () => import('../views/canvas.vue') },
    { path: '/playChess', name: 'playChess', component: () => import('../views/playChess.vue') },
    { path: '/details/:id', name: 'details', component: () => import('../views/details.vue') },
    { path: '/scss', name: 'scss', component: () => import('../views/scss.vue') },
    { path: '/boxShell', name: 'boxShell', component: () => import('../views/boxShell.vue') },
    
    { path: '/myHome/index', name: 'index', component: () => import('../views/myHome/index.vue') },
]


const router = createRouter({
    history: createWebHashHistory(), // 路由模式
    routes
})

export default router
