// 每个状态管理文件都要引入此方法
import { defineStore } from 'pinia'

// interface Item {
//     id?: number,
//     content?: string
// }

// 官方建议取名遵从 useXXXStore 形式
// 'home' 为当前store的唯一标识 类似ID 
// 取名建议与文件名称一致 便于记忆和管理
// pinia舍弃了冗长的mutations属性 
// 以下是pinia的一种写法 因与vuex相似 便于学习和记忆
export const useHomeStore = defineStore('home', {
    state: () => ({
        num: 0,
        permCodeList: [],
    }),
    getters: {
        getNum: state => state.num,
        // 获取
        getPermCodeList(): any {
            return this.permCodeList;
        },
    },
    actions: {
        setPermCodeList(codeList: any) {
            this.permCodeList = codeList
        },

        // 请求权限码
        async changePermissionCode() {
            // const codeList = await getPermCode();
            const codeList = getPermCode();
            this.setPermCodeList(codeList);
        },
        changeNum() {
            this.num++
        }
    },
})


function getPermCode() {
    throw new Error('Function not implemented.  删除函数中的await');
}
