import { ref } from 'vue'
import wuwuImg from '@/assets/img/4.webp'
import waokuImg from '@/assets/img/3.webp'

let flag = ref(true)

const mySwitch = () => {
    const pre_box: any = document.querySelector('.pre-box')
    const img: any = document.querySelector("#avatar")

    if (flag.value) {
        pre_box.style.transform = "translateX(100%)"
        pre_box.style.backgroundColor = "#c0def1" // blue
        img.src = wuwuImg
    } else {
        pre_box.style.transform = "translateX(0%)"
        pre_box.style.backgroundColor = "#6ec2f3" // pink
        img.src = waokuImg
    }

    flag.value = !flag.value
}

export default mySwitch
