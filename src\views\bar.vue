<template>
    <!-- v-if控制摄像框是否显示 -->
    <QrScan ref="qrcode" @ok="getResult" @err="geterror" v-if="open" />
    <!--  点击打开摄像头 -->
    <div class="home-item" @click="clickQr">
        <van-image class="icon-left" fit="fill" src="/icon_home_qd.png" />
        <span class="custom-title"> 通行签到 </span>
        <van-image class="arrow-right" fit="fill" src="/icon_home_arrow_right.png" />
    </div>
</template>

<script lang="ts">

import { ref } from "vue";
import QrScan from '../components/qrcode.vue';
import { Toast } from "vant";
export default {
    setup() {
        const open = ref(false);

        var browser = {   // 判断浏览器内核
            versions: (function () {
                var u = navigator.userAgent;
                return {
                    trident: u.indexOf('Trident') > -1, //IE内核
                    presto: u.indexOf('Presto') > -1, //opera内核
                    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
                    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
                    weixin: u.indexOf('MicroMessenger') > -1, //是否微信 （2015-01-22新增）
                };
            })(),
        };
        const clickQr = () => {
            // 点击签到时判断该浏览器内核，谷歌、苹果、火狐、微信可以打开
            if (browser.versions.webKit || browser.versions.weixin || browser.versions.gecko) {
                open.value = true;
            } else {
                Toast('该浏览器不支持，请打开主流浏览器：谷歌、火狐，或微信内打开');
            }
        };
        const getResult = (res: any) => {
            const r = res.split('?')[1].split('=');   // 对扫码结果进行处理，res是扫码内容
            if (r[1] == 'nX8ERbN9t4EZdPbNxMirW3uYS71111') {
                // 根据内容生成的二维码，我们规定只有内容识别为这个，才可以通过，否则提示非法通行码
                open.value = false;
            } else {
                open.value = false;
                Toast('非法通行码,请重新扫描');
            }
        };
        const geterror = (e: any) => {
            Toast(e);  // 提示报错内容
        };


        return {
            clickQr,
            getResult,
            geterror
        }
    }
}
</script>

<style  scoped>
.home-item{
    padding: 20px;
    background-color: yellowgreen;
}
</style>