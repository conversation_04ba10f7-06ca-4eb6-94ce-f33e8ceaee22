<template>
    <canvas ref='canvas' height="600" width="600"></canvas>
</template>

<script setup>
import { onMounted, ref } from 'vue'

const canvas = ref();
let ctx = ref();


// canvas折线方法  moveTo
const drawCurvedLine = () => {
    ctx = canvas.value.getContext('2d');
    // const gradient = ctx.createLinearGradient(0, 0, 688, 400);
    // gradient.addColorStop(0, 'red');
    // gradient.addColorStop(1, 'blue');
    // ctx.moveTo(100, 100);
    // ctx.lineTo(200, 200);
    // // width height
    // ctx.lineTo(300, 100);
    // ctx.lineTo(400, 200);
    // ctx.lineWidth = 30;
    // ctx.strokeStyle = gradient;
    // ctx.stroke();


    let color = ctx.createRadialGradient(250, 150, 0, 250, 150, 150);
    color.addColorStop(0, '#ccc');
    color.addColorStop(1, '#000');
    ctx.fillStyle = color;
    // 圆弧
    ctx.arc(300, 200, 100, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()

    // 左眼
    ctx.beginPath();
    ctx.arc(250, 150, 20, 0, 2 * Math.PI)
    ctx.stroke();
    ctx.closePath();

    // 右眼
    ctx.beginPath();
    ctx.arc(350, 150, 20, 0, 2 * Math.PI)
    ctx.stroke()
    ctx.closePath();

    // 鼻
    ctx.beginPath();
    ctx.ellipse(300, 200, 10, 30, 0, 0, 2 * Math.PI)
    ctx.stroke()
    ctx.closePath();

    // 嘴
    ctx.beginPath();
    ctx.arc(300, 200, 80, 0, Math.PI)
    ctx.stroke()
    ctx.closePath();
}



// canvas矩形方法  rect
// const initContext = () => {
//     ctx.fillStyle = 'Green';
//     ctx.fillRect(100, 500, 100, 100);
//     ctx.fill();
// }


// const drawFillRect = () => {
//     ctx.beginPath();
//     ctx.fillStyle = 'Yellow';
//     ctx.fillRect(0, 300, 100, 100);
// }


// const drawFill = () => {
//     ctx.beginPath();
//     ctx.fillStyle = 'Red';
//     ctx.fillRect(400, 300, 100, 100);
// }

onMounted(() => {
    drawCurvedLine();
    // initContext();
    // drawFillRect();
    // drawFill()
})

</script>

<style scoped></style>
