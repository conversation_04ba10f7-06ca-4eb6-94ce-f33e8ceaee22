
<template>
    <div class="container">
        <div class="header">
            <div class="header-text">{{ title }}</div>
        </div>
        <div class="header-title">
            <text>{{ formattedInputtime }}</text>
            <text>浏览：{{ hits }}</text>
        </div>
        <div class="content-area" v-html="content"></div>
    </div>
</template>
  


<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

interface Item {
    title: string;
    formattedInputtime: string;
    hits: number;
    thumb: string;
    content: string;
}


const item = ref<Item | null>(null);
const title = ref('');
const formattedInputtime = ref('');
const hits = ref('');
const thumb = ref('');
const content = ref('');
console.log(content,'content')

onMounted(() => {
    const route = useRoute();
    const parsedItem = JSON.parse(route.query.item) as Item;
    console.log(parsedItem, 'parsedItem');


    item.value = parsedItem;
    title.value = parsedItem.title;
    formattedInputtime.value = parsedItem.formattedInputtime;
    hits.value = parsedItem.hits;
    thumb.value = parsedItem.thumb;
    content.value = parsedItem.content;
});

</script>


<style scoped>
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.header {
    width: 70%;
    text-align: center;
    margin-top: 50px;
    padding: 20px 0;
    border-bottom: 1px solid #ccc;
}

.header-text {
    font-size: 20px;
    font-weight: bold;
}

.header-title {
    width: 70%;
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid #ccc;
}

.header-title text {
    padding: 0 10px;
}

.content-area {
    width: 70%;
    padding: 30px 0;
}

.image-box {
    margin: 10px 0;
}
</style>