<template>
  <div>
    <div>
      <router-link to="/page">跳转到page页</router-link><br />
      <router-link to="/MsMap">Go to HTML Page</router-link><br />
      <router-link to="/login">登录页</router-link>
    </div>
    <button @click="sendEvent">发送事件</button>

    <el-card class="center-card" ref="centerCardRef">
      <div>
        <el-table :ref="tableRef" :data="tableData" style="width: 100%" :height="700" ref="table"
          :row-class-name="tableRowClassName" stripe>
          <el-table-column prop="date" label="日期" width="180"> </el-table-column>
          <el-table-column prop="name" label="姓名" width="180"> </el-table-column>
          <el-table-column prop="address" label="地址"> </el-table-column>
        </el-table>
        <span class="to-top" @click="handleToTop">111</span>
        <el-icon>
          <CaretTop />
        </el-icon>
        <el-backtop :right="100" :bottom="100" @click="handleToTop" />
      </div>
    </el-card>
    <el-upload action="#" list-type="picture-card" :auto-upload="false">
      <el-icon>
        <Plus />
      </el-icon>
      <template #file="{ file }">
        <div>
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <el-icon><zoom-in /></el-icon>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
              <el-icon>
                <Download />
              </el-icon>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
              <el-icon>
                <Delete />
              </el-icon>
            </span>
          </span>
        </div>
      </template>
    </el-upload>

    <div style="display: flex; justify-content: space-around; padding: 20px 0;">
      <el-button @click="openDivitable" type="success">传递参数1</el-button>
      <el-button @click="handleQuote" type="success">业务报价2</el-button>
    </div>
  </div>
  <tableDialog :form="formDate" :dialog-visiable="toExamineItem" title="批量审核" @close="dialogClose" />
  <ChangeQuoteDialog :name="nameValue" :dialog-visiable="itemQuote" title="修改业务报价" @close="dialogClose" />
</template>

<script setup lang="ts">
import EventBus from '@/event-bus';
import { ref, onMounted, onBeforeUnmount, reactive } from 'vue';
import { Delete, Download, Plus, ZoomIn } from '@element-plus/icons-vue';
import type { UploadFile } from 'element-plus';
import ChangeQuoteDialog from "../components/notification.vue";
import tableDialog from "../components/notification.vue";
import Sortable from "sortablejs";

interface RowProps {
  rowIndex: number;
}

const toExamineItem = ref(false)
const openDivitable = () => {
  toExamineItem.value = true
}

const formDate = reactive({
  name: '张三',
  region: '土木',
  date1: '2024-03-01',
  checkedCities: [2, 4],
  delivery: true,
  resource: 1,
  desc: '123'
})

const itemQuote = ref(false)
const nameValue = ref()
async function handleQuote() {
  itemQuote.value = true;
  nameValue.value = "张三"
}

const dialogClose = () => {
  toExamineItem.value = false
  itemQuote.value = false;
}

const tableData = ref<any>([
  {
    date: '2016-05-02',
    name: 'beiid',
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    date: '2016-05-04',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    date: '2016-05-01',
    name: '北京举行虎',
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    date: '2016-05-03',
    name: '大师课烂大街',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const disabled = ref(false)
const currentPage = ref(4);
const table = ref(null);
const tableRef = ref()

const sortableList = () => {
  const el = document.querySelector('.el-table__body-wrapper tbody');

  let isDragging = false;

  const sortable = new Sortable(el, {
    animation: 150,
    ghostClass: 'blue-background-class',
    chosenClass: 'sortable-chosen',
    dragClass: 'sortable-drag',
    delay: 200,
    delayOnTouchStart: true,
    fallbackTolerance: 5,
    // 只有按住鼠标并移动一定距离才开始拖拽
    onChoose: function () {
      isDragging = false;
    },
    onStart: function () {
      isDragging = true;
      // 开始拖拽时禁用文本选择
      document.body.style.userSelect = 'none';
      document.body.style.webkitUserSelect = 'none';
    },
    onEnd: function (env: any) {
      isDragging = false;
      // 结束拖拽时恢复文本选择
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';

      if (env.oldIndex !== env.newIndex) {
        const movedItem = tableData.value.splice(env.oldIndex, 1)[0];
        tableData.value.splice(env.newIndex, 0, movedItem);
      }
    },
  });
}

// const sortableList = () => {
//   const sortable = new Sortable(document.querySelector('.el-table__body-wrapper tbody'), {
//     animation: 150,
//     ghostClass: 'blue-background-class',
//     onEnd: function (e) {
//       if (e.oldIndex !== e.newIndex) {
//         const movedItem = tableData.value.splice(e.oldIndex, 1)[0];
//         tableData.value.splice(e.newIndex, 0, movedItem);
//       }
//     }
//   });
//   console.log('sortable', sortable);
// }


onMounted(() => {
  sortableList()
  EventBus.on('another-event', (data) => {
    console.log('Received data from another event:', data);
  });
});

const sendEvent = () => {
  EventBus.emit('custom-event', { message: 'Hello from Event Bus!' });
};

const handleRemove = (file: UploadFile) => {
  console.log(file)
}

const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!
  dialogVisible.value = true
}

const handleDownload = (file: UploadFile) => {
  console.log(file)
}

// const loadTableData = () => {
//   let i = 4;
//   while (i > 0) {
//     tableData.value = tableData.value.concat([
//       {
//         date: '2016-05-02',
//         name: 'beiid',
//         address: '上海市普陀区金沙江路 1518 弄'
//       },
//       {
//         date: '2016-05-04',
//         name: '王小虎',
//         address: '上海市普陀区金沙江路 1518 弄'
//       },
//       {
//         date: '2016-05-01',
//         name: '北京举行虎',
//         address: '上海市普陀区金沙江路 1518 弄'
//       },
//       {
//         date: '2016-05-03',
//         name: '大师课烂大街',
//         address: '上海市普陀区金沙江路 1518 弄'
//       }
//     ]);
//     i--;
//   }
// };

const handleToTop = () => {
  table.value?.scrollTo({
    top: 0,
    behavior: 'smooth'  // 平滑滚动效果
  });
};


const tableRowClassName = ({ rowIndex }: RowProps) => {
  if (rowIndex === 1) {
    return 'warning-row';
  } else if (rowIndex === 3) {
    return 'success-row';
  }
  return '';
};

onBeforeUnmount(() => {
  EventBus.off('another-event');
}); 
</script>
<style lang="scss" scoped>
.top-card {
  position: sticky;
  top: 0;
  z-index: 20;
  margin-bottom: 15px;

  .end {
    display: flex;
    justify-content: flex-end;
  }

  .search-wrap {
    display: flex;
    padding: 10px 0 10px 10px;

    .btns {
      margin-right: 20px;
    }

    .search-item {
      margin-right: 20px;
    }
  }
}

.center-card {
  .pagination-wrap {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    padding-bottom: 10px;
  }
}

.to-top {
  position: sticky;
  bottom: 15%;
  left: 90%;
  z-index: 20;
  font-size: 30px;
  color: #74b9ff;
  cursor: pointer;

  i {
    transform: rotate(180deg);
  }
}

::v-deep .el-table th.el-table__cell {
  background-color: #9fd6ff;
  color: #000000;
}
</style>
