<template>
  <div class="container" @mousemove="move">
    <!-- <div class="cursor" :style="{ left: x + 'px', top: y + 'px' }"></div> -->

    <button @click="relogin">点击发送GET请求</button>
    <template>
      <div></div>
    </template>
  </div>
</template>
<script lang="ts">
import axios from 'axios';
import { onMounted } from 'vue';
import { ref } from 'vue';
export default {
  setup() {
    const x = ref(0);
    const y = ref(0);
    function move(e: any) {
      x.value = e.clientX - 150;
      y.value = e.clientY - 150;
    }

    // 新闻列表
    const relogin = async () => {
      const res = axios.get("https://api.lenxen.cn/web/article/list?exist=true&image=true");
      const req = (await res).data.data
      console.log(req, '---reginData---');
      req.forEach((i: String) => {
        console.log(i);
      });
    }


    onMounted(() => {
      relogin()
    })
    return {
      x,
      y,
      move,
      relogin,
    };
  },
};
</script>
<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cursor {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background-color: rgb(136, 155, 144);
}
</style>