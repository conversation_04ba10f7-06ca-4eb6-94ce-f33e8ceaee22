<template>
    <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="行业咨询" name="first">
                <div class="contain">
                    <template v-for="item in displayedData" :key="item.id">
                        <div class="list-box">
                            <div style="display: flex; flex-direction: column;">
                                <div class="list-text">
                                    <div class="box-img">
                                        <img :src="item.thumb" style="width: 100px; height: 100px;" alt="">
                                    </div>
                                    <div class="box-content">
                                        <p class="box-text">{{ item.title }}</p>
                                        <p class="box-textTwo">{{ getPlainText(item.content) }}</p>
                                    </div>
                                </div>
                                <div class="box-time">
                                    <p>{{ item.formattedInputtime }}</p>
                                    <p class="moreAndmore" @click="goToDetails">更多</p>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </el-tab-pane>
            <el-tab-pane label="建站知识" name="second">
                <div class="contain">
                    2
                </div>
            </el-tab-pane>
            <el-tab-pane label="优化推广" name="third">
                <div class="contain">
                    3
                </div>
            </el-tab-pane>
            <el-tab-pane label="常见问题" name="fourth">
                <div class="contain">
                    4
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="width: 100%; display: flex; justify-content: center;">
            <el-pagination background layout="prev, pager, next" :total="totalDataCount" :page-size="pageSize"
                @current-change="handlePageChange" />
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import type { TabsPaneContext } from 'element-plus';
import axios from 'axios';

// 接口响应数据类型
interface ResponseItem {
    id: number;
    thumb: string;
    title: string;
    content: string;
    date: number;
    inputtime: number;
    formattedInputtime: string;
}
const responseData = ref<ResponseItem[]>([]);
const currentPage = ref(1); // 当前页码
const pageSize = 10; // 每页显示的数据条数
const displayedData = ref<ResponseItem[]>([]); // 当前页显示的数据
const totalDataCount = ref(0); // 总数据条数
const activeName = ref('first');

const handleClick = (tab: TabsPaneContext, e: Event) => {
    console.log(tab, e);
};

const getPlainText = (html: string): string => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    const plainText = doc.body.textContent || '';
    return plainText.replace(/\s+/g, ' ').trim(); // 使用正则表达式去除空白字符
};

const fetchData = async () => {
    try {
        const response = await axios.get('https://api.lenxen.cn/web/article/list?exist=true&image=true');
        const resList = response.data.data
        console.log(resList, '111');

        responseData.value = response.data.data.map((item: ResponseItem) => {
            const date = dayjs.unix(item.inputtime);
            const formattedInputtime = date.format('YYYY-MM-DD HH:mm:ss');
            return {
                ...item,
                formattedInputtime,
            };
        });
        totalDataCount.value = responseData.value.length;
        updateDisplayedData();
    } catch (error) {
        console.error(error);
    }
};

// 跳转新闻列表详情
const router = useRouter();
const goToDetails = (item: any) => {
    console.log(item, 'item')
    router.push({
        name: 'details',
        params: {
            id: JSON.stringify(item)
        }
    });
};

const updateDisplayedData = () => {
    const startIndex = (currentPage.value - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    displayedData.value = responseData.value.slice(startIndex, endIndex);
};

const handlePageChange = (newPage: number) => {
    currentPage.value = newPage;
    updateDisplayedData();
};
onMounted(() => {
    fetchData();

});
</script>




<style>
.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 16px;
    font-weight: 600;
}

.el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
}

.contain {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.list-box {
    display: flex;
    width: 45%;
    margin-bottom: 30px;
    justify-content: space-between;
    box-shadow: 0px -1px 8px #7cb8f0;
    border-radius: 5px;
    border: 1px solid #ccc;
    padding: 10px;
}

.list-text {
    display: flex;
}


.box-text {
    margin-top: 0;
    margin-left: 25px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.box-content {
    padding: 0 20px;
}

.box-textTwo {
    margin-left: 25px;
    font-size: 14px;
    padding-top: 18px;
    border-top: 1px solid #ccc;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}

.box-time {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    border-top: 1px solid #ccc;
    background: #f3f3f3;
    padding: 0 15px;
}

.moreAndmore {
    cursor: pointer;
}

.moreAndmore:hover {
    color: blue;
}
</style>