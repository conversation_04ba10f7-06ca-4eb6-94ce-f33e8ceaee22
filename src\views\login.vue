<template>
    <div class="bigBox">
        <div class="box" ref="box">
            <div class="pre-box">
                <h1>WELCOME</h1>
                <p>JOIN US!</p>
                <div class="img-box">
                    <img src="../assets/img/3.webp" alt="" id="avatar" />
                </div>
            </div>
            <div class="register-form">
                <div class="title-box">
                    <h1>注册</h1>
                </div>
                <el-form ref="registerFormRef" :model="registerForm" :rules="rules" label-with="5px">
                    <el-form-item prop="username" label=" ">
                        <el-input type="text" placeholder="用户名" :suffix-icon="User" v-model="registerForm.username" />
                    </el-form-item>
                    <el-form-item prop="password" label=" ">
                        <el-input type="password" placeholder="密码" :suffix-icon="Lock"
                            v-model="registerForm.password" />
                    </el-form-item>
                    <el-form-item prop="confirmPassword" label=" ">
                        <el-input type="password" placeholder="确认密码" :suffix-icon="Lock"
                            v-model="registerForm.confirmPassword" />
                    </el-form-item>
                </el-form>
                <div class="btn-box" id="draw-border">
                    <el-button type="primary" @click="register">注册</el-button>
                    <p class="addPic" @click="mySwitch">已有账号?去登录</p>
                </div>
            </div>
            <div class="login-form">
                <div class="title-box">
                    <h1>登录</h1>
                </div>
                <el-form ref="loginFormRef" :model="loginForm" :rules="rules" label-with="5px">
                    <el-form-item prop="username" label=" ">
                        <el-input type="text" placeholder="用户名" :suffix-icon="User" v-model="loginForm.username" />
                    </el-form-item>
                    <el-form-item prop="password" label=" ">
                        <el-input type="password" placeholder="密码" :suffix-icon="Lock" v-model="loginForm.password" />
                    </el-form-item>
                </el-form>
                <div class="btn-box" id="draw-border">
                    <el-button type="primary" @click="login">登录</el-button>
                    <p class="addPic" @click="mySwitch">没有账号?去注册</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Lock, User } from '@element-plus/icons-vue'
import mySwitch from '@/utils/mySwitch'
import { reactive, ref } from 'vue'
import api from '@/api/login'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { onMounted } from '@vue/runtime-core'
const router = useRouter()
const loginForm = reactive({
    username: '',
    password: ''
})
const registerForm = reactive({
    username: '',
    password: '',
    confirmPassword: ''
})

const loginFormRef = ref('')

const registerFormRef = ref('')
const rules = reactive({
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 5, message: '长度应该在3~5个字符之间', trigger: 'blur' },
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '长度应该大于6', trigger: 'blur' },
    ],
    confirmPassword: [
        { required: true, message: '请输入确认密码', trigger: 'blur' },
        { min: 6, message: '长度应该大于6', trigger: 'blur' },
    ],
})


const login = () => {
    // loginFormRef.value.validate((valid) => {
    //     if (valid) {
    //         api.loginApi(loginForm).then(res => {
    //             console.log('login', res)
    //             if (res.status === 0) {
    //                 ElMessage.success(res.message)
    //                 window.sessionStorage.setItem('token', res.token)
    router.push('/home')
    //             }
    //         }).catch(error => {
    //             console.log(error);
    //         })
    //     } else {
    //         return
    //     }
    // })
}

const register = () => {
    registerFormRef.value.validate((valid) => {
        if (valid) {
            api.registerApi(registerForm).then(res => {
                if (res.status === 0) {
                    ElMessage.success(res.message)
                }
            }).catch(error => {
                console.log(error);
            })
        } else {
            return
        }
    })
}
</script>

<style scoped>
input {
    outline: none;
}

.bigBox {
    height: 100vh;
    overflow-x: hidden;
    display: flex;
    background: linear-gradient(to right, #c0def1, #6ec2f3);
    /* background-image: url('../assets/img/1.jpg'); */
    background-size: cover;
    background-repeat: no-repeat;
}

.box {
    backdrop-filter: blur(6px);
    width: 1050px;
    height: 600px;
    display: flex;
    position: relative;
    z-index: 2;
    margin: auto;
    border-radius: 8px;
}

.pre-box {
    width: 50%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 99;
    border-radius: 4px;
    background-color: #6ec2f3;
    box-shadow: 2px 1px 19px rgba(0, 0, 0, 0.1);
    transition: 0.5s ease-in-out;
}

.pre-box h1 {
    margin-top: 150px;
    text-align: center;
    letter-spacing: 5px;
    color: white;
    user-select: none;
    text-shadow: 4px 4px 3px rgba(0, 0, 0, 0.1);
}

.pre-box p {
    height: 30px;
    line-height: 30px;
    text-align: center;
    margin: 20px 0;
    user-select: none;
    font-weight: bold;
    color: white;
    text-shadow: 4px 4px 3px rgba(0, 0, 0, 0.1);
}

.img-box {
    width: 200px;
    height: 200px;
    margin: 20px auto;
    border-radius: 50%;
    user-select: none;
    overflow: hidden;
    box-shadow: 4px 4px 3px rgba(0, 0, 0, 0.1);
}

.img-box img {
    width: 100%;
    transition: 0.5s;
}


.title-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    line-height: 500px;
}

.title-box h1 {
    color: #c7effc;
    user-select: none;
    letter-spacing: 5px;
    text-shadow: 4px 4px 3px rgba(0, 0, 0, 0.1);
}

.el-form {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.el-form-item {
    width: 65%;
}

input {
    height: 40px;
    margin-bottom: 20px;
    text-indent: 10px;
    border: 1px solid #fff;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 120px;
    backdrop-filter: blur(10px);
}

input:focus {
    color: #b0cfe9;
}

input:focus::placeholder {
    opacity: 0;
}

.login-form,
.register-form {
    flex: 1;
    height: 100%;
}

.btn-box {
    display: flex;
    justify-content: center;
    align-items: center;
}


.btn-box p {
    user-select: none;
    font-size: 14px;
    color: white;
    margin: 0 10px;
}

.btn-box p:hover {
    box-sizing: border-box;
    cursor: pointer;
}

button:hover {
    cursor: pointer;
    opacity: 0.8;
}

.addPic {
    border: 0;
    background: none;
    text-transform: uppercase;
    color: #006faf;
    font-weight: bold;
    position: relative;
    outline: none;
    padding: 10px;
    box-sizing: border-box;
}

.addPic::before,
.addPic::after {
    box-sizing: inherit;
    position: absolute;
    content: '';
    border: 2px solid transparent;
    width: 0;
    height: 0;
}

.addPic::after {
    bottom: 0;
    right: 0;
}

.addPic::before {
    top: 0;
    left: 0;
}

.addPic:hover::before,
.addPic:hover::after {
    width: 100%;
    height: 100%;
}

.addPic:hover::before {
    border-top-color: #006faf;
    border-right-color: #006faf;
    transition: width 0.3s ease-out, height 0.3s ease-out 0.3s;
}

.addPic:hover::after {
    border-bottom-color: #006faf;
    border-left-color: #006faf;
    transition: border-color 0s ease-out 0.6s, width 0.3s ease-out 0.6s, height 0.3s ease-out 1s;
}
</style>