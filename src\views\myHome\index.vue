<template>
    <div class="app-container">
        <div class="map-container">
            <div class="photos" ref="container">
                <div class="photos_line" v-for="(line, lineIndex) in photoLines" :key="lineIndex">
                    <div class="photos_line_photo" v-for="(photo, photoIndex) in line" :key="photoIndex">
                        <img :src="photo.src" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { gsap } from 'gsap'

interface Photo {
    src: string
}

interface ImgData {
    node: HTMLElement
    x: number
    y: number
    mov_x: number
    mov_y: number
    ani: any
}

const container = ref<HTMLElement | null>(null)
const photoLines = ref<Photo[][]>([
    [
        { src: '/src/assets/photos/photo (1).png' },
        { src: '/src/assets/photos/photo (2).png' },
        { src: '/src/assets/photos/photo (3).png' },
        { src: '/src/assets/photos/photo (4).png' },
        { src: '/src/assets/photos/photo (5).png' },
        { src: '/src/assets/photos/photo (6).png' },
        { src: '/src/assets/photos/photo (7).png' },
    ],
    [
        { src: '/src/assets/photos/photo (8).png' },
        { src: '/src/assets/photos/photo (9).png' },
        { src: '/src/assets/photos/photo (10).png' },
        { src: '/src/assets/photos/photo (11).png' },
        { src: '/src/assets/photos/photo (12).png' },
        { src: '/src/assets/photos/photo (13).png' },
        { src: '/src/assets/photos/photo (14).png' },
    ],
    [
        { src: '/src/assets/photos/photo (15).png' },
        { src: '/src/assets/photos/photo (16).png' },
        { src: '/src/assets/photos/photo (17).png' },
        { src: '/src/assets/photos/photo (18).png' },
        { src: '/src/assets/photos/photo (19).png' },
        { src: '/src/assets/photos/photo (20).png' },
        { src: '/src/assets/photos/photo (21).png' },
    ],
    [
        { src: '/src/assets/photos/photo (22).png' },
        { src: '/src/assets/photos/photo (23).png' },
        { src: '/src/assets/photos/photo (24).png' },
        { src: '/src/assets/photos/photo (25).png' },
        { src: '/src/assets/photos/photo (26).png' },
        { src: '/src/assets/photos/photo (27).png' },
        { src: '/src/assets/photos/photo (28).png' },
    ],
])

const imgData = ref<ImgData[]>([])
let ifMovable = ref(false)
let mouseX = ref(0)
let mouseY = ref(0)
let containerWidth = ref(0)
let containerHeight = ref(0)
let photoWidth = ref(0)
let photoHeight = ref(0)
const standardWidth = 1440
let scaleNums = ref(1)

const resize = () => {
    if (container.value) {
        const imgs = [...container.value.querySelectorAll('.photos_line_photo')]
        containerWidth.value = container.value.offsetWidth
        containerHeight.value = container.value.offsetHeight
        photoWidth.value = imgs[0]?.offsetWidth ?? 0
        photoHeight.value = imgs[0]?.offsetHeight ?? 0
        scaleNums.value = document.body.offsetWidth / standardWidth

        gsap.to(imgs, {
            transform: 'translate(0, 0)',
            duration: 0,
            ease: 'power4.out'
        })

        imgData.value = []
        imgs.forEach(img => {
            imgData.value.push({
                node: img,
                x: img.offsetLeft,
                y: img.offsetTop,
                mov_x: 0,
                mov_y: 0,
                ani: 0
            })
        })
    }
}

const move = (x: number, y: number) => {
    if (!ifMovable.value) return

    const distanceX = (x - mouseX.value) / scaleNums.value
    const distanceY = (y - mouseY.value) / scaleNums.value

    imgData.value.forEach((img) => {
        let duration = 1
        img.mov_x += distanceX

        if (img.x + img.mov_x > containerWidth.value) {
            img.mov_x -= containerWidth.value
            duration = 0
        }

        if (img.x + img.mov_x < -photoWidth.value) {
            img.mov_x += containerWidth.value
            duration = 0
        }

        img.mov_y += distanceY

        if (img.y + img.mov_y > containerHeight.value) {
            img.mov_y -= containerHeight.value
            duration = 0
        }

        if (img.y + img.mov_y < -photoHeight.value) {
            img.mov_y += containerHeight.value
            duration = 0
        }

        if (img.ani) img.ani.kill()
        img.ani = gsap.to(img.node, {
            transform: `translate(${img.mov_x}px, ${img.mov_y}px)`,
            duration: duration,
            ease: 'power4.out'
        })
    })

    mouseX.value = x
    mouseY.value = y
}

const init = () => {
    resize()
    window.addEventListener('resize', resize)

    if (container.value) {
        container.value.addEventListener('mousedown', (event: MouseEvent) => {
            ifMovable.value = true
            mouseX.value = event.clientX
            mouseY.value = event.clientY
        })

        container.value.addEventListener('mouseup', () => {
            ifMovable.value = false
        })

        container.value.addEventListener('mouseleave', () => {
            ifMovable.value = false
        })

        container.value.addEventListener('mousemove', (event: MouseEvent) => {
            move(event.clientX, event.clientY)
        })
    }
}

onMounted(() => {
    init()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', resize)
    if (container.value) {
        container.value.removeEventListener('mousedown', () => { })
        container.value.removeEventListener('mouseup', () => { })
        container.value.removeEventListener('mouseleave', () => { })
        container.value.removeEventListener('mousemove', () => { })
    }
})
</script>

<style lang="scss" scoped>
.app-container {
    margin: 0;
    padding: 0;
}

.map-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    background-color: #171717;
    overflow: hidden;
}

div {
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
}


img {
    pointer-events: none;
    user-select: none;
}

.photos {
    position: absolute;
    flex-direction: column;
    overflow: hidden;
    cursor: pointer;
}

.photos_line {
    font-size: 1px;
    height: 342em;
    margin-bottom: 48em;
    flex-shrink: 0;
}

.photos_line_photo {
    font-size: 1px;
    width: 234em;
    height: 100%;
    margin-right: 36em;
    border-radius: 15em;
    background-color: #17f700;
    overflow: hidden;
    flex-shrink: 0;
}

.photos_line_photo img {
    height: 100%;
    transition: 0.3s ease;
}

.photos_line_photo:hover img {
    transform: scale(1.2);
}

@media screen and (max-aspect-ratio: 1.5/1) {

    .photos_line,
    .photos_line_photo {
        font-size: 2px;
    }
}

@media screen and (max-aspect-ratio: 0.8/1) {

    .photos_line,
    .photos_line_photo {
        font-size: 2.8px;
    }
}
</style>