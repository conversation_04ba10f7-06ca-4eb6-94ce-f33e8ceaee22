<template>
    <HelloWorld />

    <router-link to="/">跳转到首页</router-link><br>
    <div>
        <div v-if="eventReceived">Received: {{ eventMessage }}</div>
    </div>
    <div>
        <h3>姓名：{{ state.obj.name }}</h3>
        <h3>年龄：{{ state.age }}</h3>
        <h3>部门：{{ state.count.pay }}</h3>
        <button @click="state.obj.name += '~'">修改姓名</button><br>
        <button @click="state.age++">修改年龄</button><br>
        <button @click="state.count.pay += '！'">修改部门</button>
        <child-component ref="child" />
        <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
            <li v-for="i in count" :key="i" class="infinite-list-item">{{ i }}</li>
        </ul>
        <div style="padding: 40px 0;"></div>
        <el-button @click="resetDateFilter">重置时间排序</el-button>
        <el-button @click="clearFilter">格式化设置</el-button>
        <el-table ref="tableRef" row-key="date" :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="时间" sortable width="180" column-key="date"
                :filter-method="filterHandler" :filters="[
                    { text: '2016-05-01', value: '2016-05-01' },
                    { text: '2016-05-02', value: '2016-05-02' },
                    { text: '2016-05-03', value: '2016-05-03' },
                    { text: '2016-05-04', value: '2016-05-04' },
                ]" />
            <el-table-column prop="name" label="姓名" width="180" />
            <el-table-column prop="add" label="排行" :formatter="formatter" />
            <el-table-column prop="tag" label="筛选" width="100" :filters="[
                { text: 'Home', value: 'Home' },
                { text: 'Office', value: 'Office' },
            ]" :filter-method="filterTag" filter-placement="bottom-end">
                <template #default="scope">
                    <el-tag :type="scope.row.tag === 'Home' ? '' : 'success'" disable-transitions>{{ scope.row.tag
                        }}</el-tag>
                </template>
            </el-table-column>
        </el-table>
        <div style="padding: 40px 0;"></div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="首页" name="first">首页内容</el-tab-pane>
            <el-tab-pane label="标题" name="second">标题中心</el-tab-pane>
            <el-tab-pane label="排行" name="third">排行榜</el-tab-pane>
            <el-tab-pane label="设置" name="fourth">设置中心</el-tab-pane>
        </el-tabs>

        <!-- loading加载组件 -->
        <el-table v-loading="loading" :data="tableloading" style="width: 100%">
            <el-table-column prop="date" label="Date" width="180" />
            <el-table-column prop="name" label="Name" width="180" />
            <el-table-column prop="address" label="Address" />
        </el-table>

        <div>{{ userName }}</div>
        <div>{{ user.name }}</div>
        <h3 ref="refs">refs中的值</h3>
        <button @click="gai">修改名字</button>
        <button @click="dom">获取dom</button>

        <div>{{ account.zhanghao }}:{{ account.money }}</div>
        <button @click="qian">点击添加价钱</button>
        <button @click="getData">获取接口数据</button>
        <el-button :plain="true" @click="open">点击出现提示框</el-button>
        <el-button plain @click="open1">左侧出现提示框</el-button>

        <HelloEchars />
    </div>
    <router-view />
</template>

<script lang="ts">
import { markRaw, onMounted, reactive, ref, h, onBeforeUnmount } from "vue";
import EventBus from '@/event-bus';
import HelloWorld from "@/components/HelloWorld.vue";
import HelloEchars from "@/components/echars.vue";
import api from "../api/api.ts";
import { TableColumnCtx, TableInstance, TabsPaneContext, ElMessage, ElNotification } from 'element-plus'
export default {
    name: "App",
    components: {
        HelloWorld,
        HelloEchars
    },

    setup() {


        // 左侧提示框
        const open1 = () => {
            ElNotification({
                title: 'title',
                message: h('i', { style: 'color:teal' }, 'this is a reminder'),
            })
        }

        // 提示框
        const open = () => {
            ElMessage('this is a message');
        }

        // 获取接口参数内容
        let infoList = ref([])
        const getData = function () {
            api.get('/information/manage/gw/list', account).then((res) => {
                infoList.value = res.data


            }).catch(() => {
                console.log(111);
            })
        }
        onMounted(() => {
            getData()
            EventBus.on('custom-event', (data) => {
                eventReceived.value = true;
                eventMessage.value = data.message;
            })
        })

        onBeforeUnmount(() => {
            EventBus.off('custom-event');
        });
        // 点击添加价钱
        const account = reactive({
            "zhanghao": '孟迪',
            'money': 10000
        })
        const qian = () => {
            account.money = 9999
        }


        const userName = ref('孟川')
        const user = ref({ name: '孟迪' })
        const refs = ref(null)
        const gai = () => {
            console.log(userName);
            user.value.name = "孟瑶"
        }
        const dom = () => {
            console.log(refs.value);

        }

        // 声明参数类型
        interface User {
            date: string
            name: string
            add: string
            tag: string
        }
        const tableRef = ref<TableInstance>()
        // 加载组件
        const loading = ref(true)

        const activeName = ref('first')
        const handleClick = (tab: TabsPaneContext, event: Event) => {
            console.log(tab, event)
        }

        const resetDateFilter = () => {
            tableRef.value!.clearFilter(['date'])
        }


        // 重置时间列表
        const clearFilter = () => {
            tableRef.value!.clearFilter()
        }
        const formatter = (row: any, column: TableColumnCtx<User>) => {
            return row.add, column
        }
        const filterTag = (value: string, row: any) => {
            return row.tag === value
        }

        const filterHandler = (
            value: string,
            row: any,
            column: TableColumnCtx<User>
        ) => {
            const property = column['property']
            return row[property] === value
        }
        const tableloading = [
            {
                date: '2016-05-02',
                name: 'John Smith',
                address: 'No.1518,  Jinshajiang Road, Putuo District',
            },
            {
                date: '2016-05-04',
                name: 'John Smith',
                address: 'No.1518,  Jinshajiang Road, Putuo District',
            },
            {
                date: '2016-05-01',
                name: 'John Smith',
                address: 'No.1518,  Jinshajiang Road, Putuo District',
            },
        ]
        const tableData: User[] = [
            {
                date: '2016-05-03',
                name: '王小二',
                add: 'No. 189',
                tag: 'Home',
            },
            {
                date: '2016-05-02',
                name: '王小二',
                add: 'No. 189',
                tag: 'Office',
            },
            {
                date: '2016-05-04',
                name: '王小二',
                add: 'No. 189',
                tag: 'Home',
            },
            {
                date: '2016-05-01',
                name: '王小二',
                add: 'No. 189',
                tag: 'Office',
            },
        ]

        const count = ref(0)
        const load = () => {
            count.value += 2
        }
        // 定义了一段数据
        const state = reactive({
            age: 18,
            count: {
                pay: "教育部"
            },
            obj: {
                name: "张三",
            }
        });
        markRaw(state.obj)
        console.log(state.hasOwnProperty('__v_raw')) // false
        state.obj.name = '李四'
        console.log(state.obj.name);

        const eventReceived = ref(false);
        const eventMessage = ref('');
        // 将数据返回出去
        return {
            state, count, tableData, activeName,
            loading, tableloading, load, resetDateFilter,
            clearFilter, formatter, filterTag, filterHandler,
            handleClick, userName, user, refs, gai,
            dom, qian, account, infoList, getData, open, open1
        }
    },
};
</script>

<style scoped>
.infinite-list {
    height: 300px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.infinite-list .infinite-list-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    background: var(--el-color-primary-light-9);
    margin: 10px;
    color: var(--el-color-primary);
}

.infinite-list .infinite-list-item+.list-item {
    margin-top: 10px;
}

.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

body {
    margin: 0;
}

.example-showcase .el-loading-mask {
    z-index: 9;
}
</style>