<template>
    <div>
        <div class="box">
            <li class="first">zhuang</li>
        </div>
        <div class="home">
            <PostList :posts="posts" v-if="showPost" />
            <button @click="showPost = !showPost">显示或隐藏postList组件</button>
            <button @click="posts.pop()">删除一个博客信息</button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PostList from "@/components/PostList.vue";
const showPost = ref(true);
const posts = ref([
    { title: "标题1", body: "vue1", id: 1 },
    { title: "标题2", body: "lore200ddddd22222222222333", id: 2 },
    { title: "标题3", body: "vue3", id: 3 },
    { title: "标题4", body: "vue4", id: 4 },
]);
</script>

<style scoped>
.box {
    border: 1px solid #b2b2b2;
    width: 400px;
    display: flex;
    flex-wrap: wrap;
    transform: rotateY('180deg');
}

li {
    min-width: 100px;
    background-color: bisque;
    transition: 1s all;
    margin: 5px;
}

.box:hover .first {
    background-color: rgb(46, 190, 17);
    transform: rotateY('180deg');
}

.block {
    width: 200px;
    height: 200px;
    background: brown;
    cursor: pointer;
    transition: 0.8s;

}

.block:hover {
    transform: rotateY(180deg);
}
</style>