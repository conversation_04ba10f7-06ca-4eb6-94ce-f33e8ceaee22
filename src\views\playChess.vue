<template>
    <div>
        <div class="tip">黑棋先手</div>
        <canvas id="canvas" ref='canvas' height="800" width="800">
        </canvas>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'

const canvas = ref();
let ctx = ref();

const Playchess = () => {
    ctx.value = canvas.value.getContext('2d');
    // 1. 棋盘
    for (let i = 1; i < 16; i++) {
        // 横线 
        ctx.value.moveTo(50, 50 * i);
        ctx.value.lineTo(750, 50 * i);
        ctx.value.stroke();

        // 竖线
        ctx.value.moveTo(50 * i, 50);
        ctx.value.lineTo(50 * i, 750);
        ctx.value.stroke()

    }
}


onMounted(() => {
    Playchess();

    // 获取tip元素
    let tiped = document.querySelector('.tip')

    // 4. 使用一二维数组保存所有棋子
    let circles = [];
    for (let i = 1; i < 16; i++) {
        circles[i] = [];
    }

    // 3. 声明变量保存黑子
    let isBlack = true;

    // 定义变量保存胜利
    let endGame = false;


    // 2. 点击棋盘生成棋子
    const hitCanvas = canvas.value;
    hitCanvas.addEventListener('click', e => {
        let { offsetX, offsetY } = e;
        if (offsetX < 25 || offsetY < 25 || offsetX > 775 || offsetY > 775) {
            return;
        }
        let i = Math.floor((offsetX + 25) / 50);
        let j = Math.floor((offsetY + 25) / 50);

        if (endGame) {
            // 如果有五颗相同颜色棋子连接在一起时结束
            return;
        }

        // 5. 判断当前位置是否已经存在棋子
        if (circles[i][j]) {
            tiped.innerText = `这里不能重复落子。当前是${isBlack ? '黑' : '白'}子的回合`
            return
        }

        let x = i * 50;
        let y = j * 50;
        ctx.value.beginPath()  // 与上一段canvas脱离关系
        ctx.value.arc(x, y, 20, 0, 2 * Math.PI);

        // 把对应棋子存到二维数组里面
        circles[i][j] = isBlack ? 'black' : 'white';

        // 根据当前输入棋子的颜色，切换黑白棋子
        let tx = isBlack ? x - 10 : x + 10;
        let ty = isBlack ? y - 10 : y + 10;
        let gread = ctx.value.createRadialGradient(tx, ty, 0, tx, ty, 30);
        gread.addColorStop(0, isBlack ? '#ccc' : '#666');
        gread.addColorStop(1, isBlack ? '#000' : '#fff');
        ctx.value.fillStyle = gread;
        ctx.value.fill();
        ctx.value.closePath()

        // 7. 判断当前是否已经有对象的棋子连城5个一条线
        endGame = checkVertical(i, j) || checkHorizontal(i, j) || checkNW2SE(i, j) || checkNE2SW(i, j)
        if (endGame) {
            tiped.innerText = `${isBlack ? '黑' : '白'}棋已获胜，刷新重新开始`
            return;
        }


        // 6. 提醒对方落子
        tiped.innerText = isBlack ? '轮到白棋落子' : '轮到黑棋落子';
        isBlack = !isBlack;
    });

    // 纵向胜利条件
    function checkVertical(row, col) {
        // 定义变量记录向上的次数
        let up = 0;
        let down = 0;
        let times = 0;
        // 定义当前棋子连接颗数
        let count = 1;
        while (times < 10000) {
            times++;
            // 如果棋子已经大于一个指定的次数
            if (count >= 5) {
                break;
            }

            let target = isBlack ? 'black' : 'white'
            up++;
            if (circles[row][col - up] && circles[row][col - up] == target) {
                count++;
            }
            down++;
            if (circles[row][col + down] && circles[row][col + down] === target) {
                count++;
            }
            if (count >= 5 || (circles[row][col - up] !== target && circles[row][col + down] == target)) {
                break;
            }
        }
        return count >= 5;
    }
    // 横向胜利条件
    function checkHorizontal(row, col) {
        // 定义变量记录向上的次数
        let left = 0;
        let right = 0;
        // 定义当前棋子连接颗数
        let count = 1;
        let times = 0;
        while (times < 10000) {
            times++;

            let target = isBlack ? 'black' : 'white'

            left++;
            if (circles[row - left][col] && circles[row - left][col] == target) {
                count++;
            }
            right++;
            if (circles[row + right][col] && circles[row + right][col] === target) {
                count++;
            }
            if (count >= 5 || (circles[row - left][col] !== target && circles[row + right][col] !== target)) {
                break;
            }
        }
        return count >= 5;
    }
    // 右斜向胜利条件  补充条件判断是否落子在网格区域内
    function checkNW2SE(row, col) {
        let lt = 0;
        let rb = 0;

        let target = isBlack ? 'black' : 'white';

        let count = 1;

        let times = 0;
        while (times < 10000) {
            times++;

            lt++;
            if (circles[row - lt][col - lt] && circles[row - lt][col - lt] === target) {
                count++;
            }
            rb++;
            if (circles[row + rb][col + rb] && circles[row + rb][col + rb] === target) {
                count++;
            }
            if (count >= 5 || (circles[row - lt][col - lt] !== target && circles[row + rb][col + rb] !== target)) {
                break;
            }
        }
        return count >= 5;
    }
    // 左斜向胜利条件
    function checkNE2SW(row, col) {
        let rt = 0;
        let lb = 0;

        let target = isBlack ? 'black' : 'white';

        let count = 1;

        let times = 0;
        while (times < 10000) {
            times++;

            rt++;
            if (circles[row + rt][col - rt] && circles[row + rt][col - rt] === target) {
                count++;
            }
            lb++;
            if (circles[row - lb][col + lb] && circles[row - lb][col + lb] === target) {
                count++;
            }
            if (count >= 5 || (circles[row + rt][col - rt] !== target && circles[row - lb][col + lb] !== target)) {
                break;
            }
        }
        return count >= 5;
    }
})
</script>

<style scoped>
canvas {
    background-color: rgb(224, 221, 218);
}

.tip {
    text-align: center;
    padding: 20px;
}
</style>