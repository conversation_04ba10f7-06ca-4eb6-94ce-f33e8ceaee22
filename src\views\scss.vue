<template>
    <div>
        MAGICCSS
    </div>
</template>

<script setup lang="ts">

</script>

<style scoped>
div {
    position: relative;
    width: 640px;
    height: 120px;
    color: #e90000;
    font-size: 124px;
    text-align: center;
    margin: 140px auto;
    border-bottom: 10px solid #bb1b1b;
    transform: skewY(5deg);

    &::before,
    &::after {
        position: absolute;
        content: "";
        bottom: -20px;
        left: 0;
        width: 10px;
        height: 20px;
        border-radius: 50%;
        background: #b30404;
        transform: translate(0, 0);
        animation: move 3.5s ease-in-out infinite;
    }
    &::before {
        position: absolute;
        content: "";
        bottom: -20px;
        left: 0;
        width: 10px;
        height: 20px;
        border-radius: 50%;
        background: #b30404;
        transform: translate(0, 0);
        animation: move 3.5s ease-in-out infinite;
    }

    &::after {
        animation: move 7.5s ease-in-out 1s infinite;
    }
}

@keyframes move {
    80% {
        bottom: -30px;
        transform: translate(623px, 0);
    }

    93% {
        transform: translate(623px, 3px);
        opacity: 1;
    }

    100% {
        transform: translate(623px, 150px);
        opacity: 0;
    }
}
</style>