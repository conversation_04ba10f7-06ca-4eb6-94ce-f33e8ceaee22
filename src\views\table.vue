<template>
    <div class="calculator">
        <div class="display">{{ contentValue }}</div>
        <div class="keypad">
            <button class="operator" @click="parenthesis('(')">(</button>
            <button class="operator" @click="parenthesis(')')">)</button>
            <button class="operator" @click="PercentageSign()">%</button>
            <button class="clear" @click="clear()">AC</button>
            <button @click="num(7)">7</button>
            <button @click="num(8)">8</button>
            <button @click="num(9)">9</button>
            <button class="operator" @click="operator('+')">+</button>
            <button @click="num(4)">4</button>
            <button @click="num(5)">5</button>
            <button @click="num(6)">6</button>
            <button class="operator" @click="operator('-')">-</button>
            <button @click="num(1)">1</button>
            <button @click="num(2)">2</button>
            <button @click="num(3)">3</button>
            <button class="operator" @click="operator('*')">*</button>
            <button @click="num(0)">0</button>
            <button @click="decimalPoint()">.</button>
            <button class="eq" @click="calculate()">=</button>
            <button class="operator" @click="operator('/')">/</button>
        </div>
    </div>
</template>
  
<script lang="ts">
import { ref, Ref } from 'vue';

export default {
    setup() {
        const contentValue: Ref<string> = ref('0');

        // 数字按钮点击事件
        function num(number: number): void {
            if (contentValue.value === '0') {
                contentValue.value = number.toString();
            } else {
                contentValue.value += number;
            }
        }

        // 运算符按钮点击事件
        function operator(e: string) {
            contentValue.value += e;
        }

        // 小数点按钮点击事件
        function decimalPoint() {
            if (!contentValue.value.includes('.')) {
                contentValue.value += '.';
            }
        }

        // 括号按钮点击事件
        function parenthesis(e: string) {
            contentValue.value += e;
        }

        // 计算按钮点击事件
        function calculate() {
            try {
                contentValue.value = eval(contentValue.value);
            } catch (error) {
                contentValue.value = 'Error';
            }
        }

        // 百分号按钮点击事件
        function PercentageSign() {
            try {
                const result = eval(contentValue.value);
                contentValue.value = (result / 100).toString();
            } catch (error) {
                contentValue.value = 'Error';
            }
        }

        // 清除按钮点击事件
        function clear() {
            contentValue.value = '';
        }

        return {
            contentValue,
            num,
            operator,
            decimalPoint,
            parenthesis,
            calculate,
            PercentageSign,
            clear,
        };
    },
};
</script>
  
  
<style>
.calculator {
    width: 260px;
    margin: 0 auto;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: #EAEDF4;
}

.display {
    margin-bottom: 10px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #fff;
    text-align: right;
    height: 40px;
    font-size: 25px;
    line-height: 65px;
    font-weight: bold;
    overflow: hidden;

}

.keypad {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

button {
    width: 60px;
    margin: 2px;
    border-radius: 10%;
    background-color: #fff;
    border: 1px solid #ccc;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 1px 1px 1px #ccc;
    cursor: pointer;
}

button.operator {
    background-color: #E1E1EB;
}

button.clear {
    background-color: #FCB125;
    color: #fff;
}

button.eq {
    background-color: #5373F7;
    color: #fff;
}

button:hover {
    background-color: #ccc;
}
</style>