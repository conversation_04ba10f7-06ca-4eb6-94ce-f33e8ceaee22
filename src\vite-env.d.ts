/// <reference types="vite/client" />
declare module '*.vue' {
    import type { DefineComponent } from 'vue'
    import { ComponentOptions } from "vue";
    const componentOptions: ComponentOptions
    const component: ComponentOptions | ComponentOptions['setup']
    export default component; componentOptions

    interface ComponentCustomProperties {
        $filters: any,
        $http: any
    }
}


declare module "sortablejs";