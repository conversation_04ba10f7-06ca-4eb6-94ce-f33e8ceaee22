import { defineConfig } from 'vite'
import { resolve } from "path";
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import vue from '@vitejs/plugin-vue'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

export default defineConfig(() => {
  return {
    plugins: [
      vue(),
      AutoImport({
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({ prefix: 'icon' }) // 图标前缀配置
        ],
      }),
      Components({
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({ enabledCollections: ['ep'] }) // 启用Element Plus图标集
        ],
      }),
      Icons({ autoInstall: true }) // 自动安装图标集
    ],
    server: {
      proxy: {
        '/Api': {
          target: 'http://120.24.96.243:8888',
          pathRewrite: {
            '^/qualityPlatform': '/'
          },
          changeOrigin: true,
        },
      }
    },
    resolve: {
      alias: [
        {
          find: '@',  // 别名
          replacement: resolve(__dirname, 'src'),  // 别名对应地址
        },
        {
          find: 'components',
          replacement: resolve(__dirname, 'src/components'),
        }
      ]
    }
  }
})


